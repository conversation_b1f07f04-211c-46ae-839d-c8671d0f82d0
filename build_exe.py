#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激光模板查询器 - EXE打包脚本
使用PyInstaller将应用打包成独立的可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("📦 正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyInstaller安装失败")
        return False

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['run.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('laser_templates.json', '.'),
        ('sample_data.json', '.'),
        ('main.py', '.'),
        ('data_manager.py', '.'),
        ('settings_window.py', '.'),
        ('README.md', '.'),
    ],
    hiddenimports=['tkinter', 'tkinter.ttk', 'json', 'os', 'sys'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='激光模板查询器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
    version_file=None,
)
'''
    
    with open('laser_template_query.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✅ 规格文件创建成功")

def build_exe():
    """构建EXE文件"""
    print("🔨 开始构建EXE文件...")
    try:
        # 使用规格文件构建
        subprocess.check_call([
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "laser_template_query.spec"
        ])
        print("✅ EXE文件构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False

def cleanup_build_files():
    """清理构建过程中的临时文件"""
    print("🧹 清理临时文件...")
    
    # 要删除的目录和文件
    cleanup_items = [
        'build',
        '__pycache__',
        '*.pyc',
        'laser_template_query.spec'
    ]
    
    for item in cleanup_items:
        if item.endswith('*'):
            # 处理通配符
            import glob
            for file in glob.glob(item):
                try:
                    os.remove(file)
                    print(f"  删除文件: {file}")
                except:
                    pass
        else:
            # 处理目录和文件
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
                    print(f"  删除目录: {item}")
                else:
                    os.remove(item)
                    print(f"  删除文件: {item}")

def create_release_package():
    """创建发布包"""
    print("📦 创建发布包...")
    
    # 创建发布目录
    release_dir = "release"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)
    
    # 复制EXE文件
    exe_path = os.path.join("dist", "激光模板查询器.exe")
    if os.path.exists(exe_path):
        shutil.copy2(exe_path, release_dir)
        print(f"  ✅ 复制EXE文件到: {release_dir}")
    else:
        print("  ❌ 未找到EXE文件")
        return False
    
    # 复制说明文件
    if os.path.exists("README.md"):
        shutil.copy2("README.md", release_dir)
        print("  ✅ 复制README.md")
    
    # 创建使用说明
    usage_text = """🔬 激光模板查询器 v1.7 使用说明

📋 快速开始：
1. 双击"激光模板查询器.exe"启动程序
2. 选择单位（支持下拉选择或输入搜索）
3. 选择激光要求
4. 查看并复制模板代码

🔍 智能搜索功能：
- 中文直接搜索：输入"航空"匹配"航空制造部"
- 拼音首字母搜索：输入"hk"匹配"航空制造部"
- 部分匹配搜索：输入"制造"匹配所有制造部门
- 模糊匹配：支持不完整输入的智能匹配

💡 使用技巧：
- 双击激光要求可直接复制模板代码
- 支持窗口大小调整，最小450x320像素
- 所有功能都支持键盘和鼠标操作
- 支持滚动查看，适应各种屏幕尺寸

🎯 功能特色：
- 响应式界面设计，自适应窗口大小
- 现代化扁平化UI风格
- 智能字体大小层次，重要内容更清晰
- 完整的滚动支持，小窗口也能完整使用

📁 系统要求：
- Windows 7/8/10/11
- 无需安装Python环境
- 独立可执行文件，绿色免安装

📞 技术支持：
如有问题请联系开发团队
版本：v1.7 - 完整功能版
"""
    
    with open(os.path.join(release_dir, "使用说明.txt"), 'w', encoding='utf-8') as f:
        f.write(usage_text)
    print("  ✅ 创建使用说明.txt")
    
    print(f"🎉 发布包创建完成，位置: {os.path.abspath(release_dir)}")
    return True

def main():
    """主函数"""
    print("🚀 激光模板查询器 - EXE打包工具")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists("run.py"):
        print("❌ 错误：请在项目根目录运行此脚本")
        return
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("❌ 无法安装PyInstaller，请手动安装：pip install pyinstaller")
            return
    
    # 创建规格文件
    create_spec_file()
    
    # 构建EXE
    if not build_exe():
        print("❌ 构建失败")
        return
    
    # 创建发布包
    if create_release_package():
        print("\n🎉 打包完成！")
        print("📁 EXE文件位置: release/激光模板查询器.exe")
        print("📖 使用说明: release/使用说明.txt")
        print("\n💡 提示：可以将release文件夹分发给用户使用")
    
    # 清理临时文件
    cleanup_build_files()
    
    print("\n✅ 所有操作完成！")

if __name__ == "__main__":
    main()
