#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示激光模板查询器的数据保存位置
"""

import os
import sys

def get_data_path():
    """获取数据保存路径"""
    if getattr(sys, 'frozen', False):
        # EXE环境
        app_data_dir = os.path.expanduser("~")
        app_dir = os.path.join(app_data_dir, "LaserTemplateQuery")
        data_file_path = os.path.join(app_dir, "laser_templates.json")
        return data_file_path, "EXE模式"
    else:
        # 开发环境
        return "laser_templates.json", "开发模式"

def main():
    data_path, mode = get_data_path()
    
    print("🔬 激光模板查询器 - 数据保存位置")
    print("=" * 50)
    print(f"运行模式: {mode}")
    print(f"数据保存位置: {data_path}")
    print()
    
    # 检查文件是否存在
    if os.path.exists(data_path):
        file_size = os.path.getsize(data_path)
        mod_time = os.path.getmtime(data_path)
        import datetime
        mod_time_str = datetime.datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S')
        
        print("✅ 数据文件存在")
        print(f"文件大小: {file_size} 字节")
        print(f"最后修改: {mod_time_str}")
    else:
        print("❌ 数据文件不存在")
        print("提示: 首次运行程序后会自动创建")
    
    print()
    print("💡 说明:")
    print("- EXE版本的数据保存在用户目录下，程序卸载后数据仍然保留")
    print("- 开发版本的数据保存在程序目录下")
    print("- 数据文件包含所有单位、激光要求和模板代码")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
