# 激光模板查询器 v1.0

一个用于快速查询激光加工模板代码的桌面应用程序，支持三级查询流程和完整的数据管理功能。

## 功能特点

### 主要功能
- **三级查询流程**：单位 → 激光要求 → 模板代码
- **窗口置顶**：保持窗口在最前面，方便与其他软件配合使用
- **多种选择方式**：支持下拉列表和快速按钮两种单位选择方式
- **代码操作**：复制到剪贴板、保存到文件
- **实时更新**：数据修改后立即生效

### 数据管理功能
- **完整的CRUD操作**：添加、编辑、删除单位和激光要求
- **模板代码编辑**：内置代码编辑器，支持语法高亮
- **数据持久化**：使用JSON格式存储，支持中文
- **导入导出**：支持数据备份和恢复
- **数据验证**：防止重复数据和无效输入

## 文件结构

```
激光模板查询器1.0/
├── run.py                 # 启动脚本（双击运行）
├── main.py                # 主程序文件
├── data_manager.py        # 数据管理模块
├── settings_window.py     # 设置界面模块
├── sample_data.json       # 示例数据文件
├── laser_templates.json   # 用户数据文件（自动生成）
└── README.md             # 说明文档
```

## 快速开始

### 🎯 推荐方式：直接运行EXE文件
1. 下载`release`文件夹中的`激光模板查询器.exe`
2. 双击运行，无需安装Python环境
3. 绿色免安装，即开即用

### 🔧 开发者方式：Python源码运行
1. **方法一**：双击 `run.py` 文件
2. **方法二**：在命令行中运行 `python run.py`
3. **方法三**：直接运行 `python main.py`

### 📦 自己打包EXE
```bash
python build_exe.py
```
打包完成后在`release`文件夹中找到可执行文件

### 基本使用流程
1. **选择单位**：
   - 使用下拉列表选择单位
   - 或点击快速选择按钮

2. **选择激光要求**：
   - 在激光要求列表中单击选择
   - 双击可直接复制模板代码到剪贴板

3. **查看和使用模板代码**：
   - 选择要求后自动显示对应的模板代码
   - 点击"复制代码"按钮复制到剪贴板
   - 点击"保存到文件"按钮保存为G代码文件

## 数据管理

### 打开设置界面
- 点击主界面的"数据设置"按钮
- 或使用菜单栏：设置 → 数据管理

### 管理单位
- **添加单位**：点击"添加单位"按钮，输入单位名称
- **重命名单位**：选择单位后点击"重命名单位"按钮
- **删除单位**：选择单位后点击"删除单位"按钮（会删除所有相关数据）

### 管理激光要求
- **添加要求**：选择单位后点击"添加要求"按钮
- **删除要求**：选择要求后点击"删除要求"按钮

### 编辑模板代码
1. 在设置界面中选择单位和激光要求
2. 在右侧的模板代码编辑区域修改代码
3. 点击"保存模板"按钮保存修改

### 数据导入导出
- **导出数据**：点击"导出数据"按钮，选择保存位置
- **导入数据**：点击"导入数据"按钮，选择要导入的JSON文件
- **保存所有**：点击"保存所有"按钮保存当前所有修改

## 示例数据

程序包含了丰富的示例数据，包括：

### 航空制造部
- 钛合金激光切割
- 铝合金激光焊接
- 不锈钢激光打标
- 复合材料激光切割

### 汽车制造部
- 车身钢板激光切割
- 发动机零件激光焊接
- 车标激光雕刻
- 排气管激光切割

### 电子制造部
- PCB板激光切割
- 芯片激光打标
- 导线激光焊接
- 外壳激光雕刻

### 医疗器械部
- 手术器械激光切割
- 植入物激光焊接
- 医疗标识激光打标

## 技术规格

- **开发语言**：Python 3.6+
- **GUI框架**：tkinter（Python内置）
- **数据格式**：JSON
- **编码格式**：UTF-8（完整支持中文）
- **运行环境**：Windows、macOS、Linux

## 系统要求

- Python 3.6 或更高版本
- tkinter 模块（通常随Python一起安装）
- 至少 50MB 可用磁盘空间
- 1024x768 或更高分辨率显示器

## 常见问题

### Q: 程序无法启动怎么办？
A: 请检查：
1. Python版本是否为3.6或更高
2. 所有程序文件是否在同一目录下
3. 是否有足够的磁盘空间

### Q: 数据丢失了怎么办？
A: 程序会自动创建备份文件（.backup后缀），可以手动恢复。

### Q: 如何添加自定义数据？
A: 使用"数据设置"功能，或直接编辑JSON文件（需要了解JSON格式）。

### Q: 程序支持哪些G代码格式？
A: 程序本身不限制G代码格式，可以存储任何文本格式的激光加工代码。

## 更新日志

### v1.9 (2024-01-01) - 数据持久化修复
- 💾 **数据持久化保存**：
  - EXE版本数据保存到用户目录：`C:\Users\<USER>\LaserTemplateQuery\`
  - 解决EXE应用数据丢失问题，确保数据永久保存
  - 程序重启、系统重启后数据完整保留
  - 支持多用户环境，每个用户独立的数据空间
- 🔧 **智能数据管理**：
  - 自动检测运行环境（EXE vs 开发模式）
  - EXE环境：数据保存到用户AppData目录
  - 开发环境：数据保存到程序目录
  - 首次运行自动创建数据目录和初始数据
- 📁 **数据文件结构**：
  - `laser_templates.json` - 主数据文件
  - 包含所有单位、激光要求和模板代码
  - JSON格式，支持手动编辑和程序管理
- 🛡️ **数据安全保障**：
  - 自动备份机制，防止数据丢失
  - 错误恢复功能，加载失败时使用默认数据
  - 数据验证和完整性检查

### v1.8 (2024-01-01) - EXE打包发布
- 📦 **独立可执行文件**：
  - 使用PyInstaller打包成单文件EXE应用
  - 无需安装Python环境，双击即可运行
  - 包含所有依赖库和资源文件
  - 支持Windows 7/8/10/11系统
- 🛠️ **自动化打包工具**：
  - 提供`build_exe.py`一键打包脚本
  - 自动检查和安装PyInstaller依赖
  - 智能处理资源文件和模块依赖
  - 自动创建发布包和使用说明
- 📁 **发布包结构**：
  - `激光模板查询器.exe` - 主程序文件
  - `使用说明.txt` - 详细使用指南
  - `README.md` - 技术文档
- 🔧 **打包优化**：
  - 修复PyInstaller环境下的文件路径问题
  - 优化启动脚本，支持打包和开发环境
  - 包含完整的错误处理和用户提示
  - 自动清理构建临时文件

### v1.7 (2024-01-01) - 小窗口适配优化
- 📱 **极小窗口支持**：
  - 最小窗口尺寸从500x350减小到450x320
  - 优化滚动布局，确保内容宽度自适应
  - 所有边距进一步压缩（从6px减小到2px）
  - 按钮布局更紧凑，减少每行按钮数量
- 🎯 **布局优化细节**：
  - 下拉列表宽度从18减小到15字符
  - 按钮宽度和间距进一步压缩
  - 快速选择按钮每行最多5个（原来6个）
  - 所有面板内边距统一减小到2px
- ✅ **滚动机制改进**：
  - 修复画布宽度自适应问题
  - 确保滚动条在小窗口时正确显示
  - 内容框架宽度与画布同步调整
  - 防止水平内容溢出

### v1.6 (2024-01-01) - 字体层次优化
- 🔤 **字体大小层次化**：
  - **重要选择内容**：11-12号字体（下拉列表、按钮、列表项、模板代码）
  - **次要标签文字**：7号字体（"下拉选择"、"快速选择"、"当前模板"等标签）
  - **模板信息显示**：11号加粗字体，突出当前选择状态
  - **代码显示区域**：12号Consolas字体，确保代码清晰可读
- 👁️ **视觉层次优化**：
  - 用户操作元素字体更大更清晰
  - 装饰性标签字体适当缩小
  - 重要信息突出显示
  - 整体视觉平衡和谐
- 🎯 **用户体验提升**：
  - 选择操作更加清晰易读
  - 减少视觉干扰元素
  - 提高界面信息密度
  - 保持响应式字体缩放

### v1.5 (2024-01-01) - 智能搜索功能
- 🔍 **模糊搜索实现**：
  - 单位选择支持直接输入文字进行搜索
  - 支持中文、拼音首字母、部分匹配多种搜索方式
  - 实时搜索结果过滤，即时显示匹配项
  - 智能自动补全和最佳匹配选择
- 🎯 **搜索功能特性**：
  - **中文搜索**：直接输入"航空"、"汽车"等中文
  - **拼音搜索**：输入"hk"匹配"航空"，"qc"匹配"汽车"
  - **部分匹配**：输入"制造"匹配所有包含该词的单位
  - **智能补全**：失去焦点时自动选择最佳匹配项
- 💡 **用户体验优化**：
  - 搜索提示图标和状态栏提示
  - 焦点状态智能处理
  - 无效输入自动清理
  - 搜索历史和快速访问

### v1.4 (2024-01-01) - 滚动布局和完全响应式优化
- 📜 **滚动布局实现**：
  - 添加垂直滚动条，确保小窗口时所有内容都能访问
  - 支持鼠标滚轮操作，流畅滚动体验
  - 动态滚动区域，根据内容自动调整
- 🎯 **超紧凑布局**：
  - 所有面板间距进一步减小（从8-12px减少到2-4px）
  - 列表框最小高度从3行减少到2行
  - 模板文本框最小高度从2行减少到1行
  - 按钮区域间距优化，最大化内容显示空间
- ✅ **完全响应式保证**：
  - 在500x350最小窗口下确保所有功能可用
  - 内容自动缩放，不会出现截断或遮挡
  - 保持所有交互功能的可访问性

### v1.3 (2024-01-01) - 响应式布局优化
- 📱 **响应式设计实现**：
  - 窗口大小变化时，内容自动成比例缩放
  - 最小窗口尺寸调整为500x350，确保内容完整显示
  - 字体大小根据窗口大小动态调整（6-10px范围）
  - 按钮间距和组件尺寸智能缩放
- 🔧 **智能布局调整**：
  - 单位按钮每行数量根据窗口宽度动态调整（3-6个）
  - 列表框高度根据窗口大小自适应（3-6行）
  - 模板代码显示区域高度动态调整（2-5行）
  - 所有间距和边距按比例缩放
- ⚡ **性能优化**：
  - 防抖机制避免频繁更新
  - 缩放比例限制在0.6-1.2倍之间
  - 静默错误处理确保用户体验流畅

### v1.2 (2024-01-01) - 紧凑布局优化
- 🎯 **按钮尺寸优化**：
  - 减小所有按钮的内边距和字体大小，使界面更紧凑
  - 按钮宽度从10-12调整为6-8，高度相应减小
  - 优化按钮间距，从6-12px调整为1-3px
- 📐 **布局间距紧凑化**：
  - 主框架边距从12px减少到6px
  - 各级面板间距从8-12px减少到4-6px
  - 内容区域边距从8-16px减少到4-8px
  - 状态栏高度从32px减少到24px
- 🔧 **组件布局优化**：
  - 单位按钮每行显示5个（原4个），宽度减小到8
  - 分隔线宽度从2px减少到1px，间距从20px减少到8px
  - 代码显示区域间距优化，更加紧凑
- ✨ **视觉效果保持**：
  - 保持所有现代化视觉效果和配色方案
  - 保持悬停效果和状态指示器
  - 保持所有功能完整性

### v1.1 (2024-01-01) - 界面现代化优化
- 🎨 **界面现代化改进**：
  - 采用扁平化设计风格，去除过时的3D效果
  - 使用现代化的配色方案和Unicode图标
  - 优化字体和间距，提升视觉效果
- 📐 **界面尺寸优化**：
  - 主窗口尺寸从1000x700调整为750x480，更加紧凑
  - 最小窗口尺寸从800x600调整为650x400
  - 减少不必要的空白区域，优化布局间距
- 📝 **模板显示优化**：
  - 智能切换单行/多行显示模式
  - 单行代码使用Entry组件，多行代码使用Text组件
  - 减小模板代码显示区域高度，更适合实际使用
- 🎯 **用户体验提升**：
  - 添加丰富的Unicode图标到按钮和状态提示
  - 优化颜色搭配，提高可读性
  - 改进状态栏显示，增加表情符号提示
  - 按钮布局更加合理，操作更加便捷

### v1.0 (2024-01-01)
- 初始版本发布
- 实现三级查询功能
- 完整的数据管理系统
- 窗口置顶功能
- 数据导入导出功能
- 丰富的示例数据

## 技术支持

如果您在使用过程中遇到问题，请检查：
1. 程序文件是否完整
2. Python环境是否正确安装
3. 数据文件格式是否正确

## 许可证

本程序仅供学习和内部使用。
