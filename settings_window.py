# -*- coding: utf-8 -*-
"""
设置界面模块
实现数据的添加、编辑、删除功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from data_manager import DataManager


class SettingsWindow:
    def __init__(self, parent, data_manager: DataManager, callback=None):
        """
        初始化设置窗口
        
        Args:
            parent: 父窗口
            data_manager: 数据管理器实例
            callback: 数据更新后的回调函数
        """
        self.parent = parent
        self.data_manager = data_manager
        self.callback = callback
        
        # 创建设置窗口
        self.window = tk.Toplevel(parent)
        self.window.title("激光模板数据设置")
        self.window.geometry("900x600")
        self.window.resizable(True, True)
        
        # 设置窗口图标和属性
        self.window.transient(parent)
        self.window.grab_set()
        
        # 当前选中的数据
        self.current_unit = None
        self.current_requirement = None
        
        self.create_widgets()
        self.load_data()
        
        # 窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧面板 - 单位和要求列表
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 单位管理区域
        unit_frame = ttk.LabelFrame(left_frame, text="单位管理")
        unit_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        # 单位列表
        self.unit_listbox = tk.Listbox(unit_frame, height=8)
        self.unit_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.unit_listbox.bind('<<ListboxSelect>>', self.on_unit_select)
        
        # 单位操作按钮
        unit_btn_frame = ttk.Frame(unit_frame)
        unit_btn_frame.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        ttk.Button(unit_btn_frame, text="添加单位", command=self.add_unit).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(unit_btn_frame, text="重命名单位", command=self.rename_unit).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(unit_btn_frame, text="删除单位", command=self.delete_unit).pack(side=tk.LEFT)
        
        # 激光要求管理区域
        req_frame = ttk.LabelFrame(left_frame, text="激光要求管理")
        req_frame.pack(fill=tk.BOTH, expand=True)
        
        # 要求列表
        self.req_listbox = tk.Listbox(req_frame, height=8)
        self.req_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.req_listbox.bind('<<ListboxSelect>>', self.on_requirement_select)
        
        # 要求操作按钮
        req_btn_frame = ttk.Frame(req_frame)
        req_btn_frame.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        ttk.Button(req_btn_frame, text="添加要求", command=self.add_requirement).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(req_btn_frame, text="删除要求", command=self.delete_requirement).pack(side=tk.LEFT)
        
        # 右侧面板 - 模板代码编辑
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 模板编辑区域
        template_frame = ttk.LabelFrame(right_frame, text="模板代码编辑")
        template_frame.pack(fill=tk.BOTH, expand=True)
        
        # 当前编辑信息
        info_frame = ttk.Frame(template_frame)
        info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(info_frame, text="当前编辑:").pack(side=tk.LEFT)
        self.current_info_label = ttk.Label(info_frame, text="请选择单位和要求", foreground="gray")
        self.current_info_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # 模板代码文本框
        text_frame = ttk.Frame(template_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
        
        self.template_text = tk.Text(text_frame, wrap=tk.NONE, font=("Consolas", 10))
        
        # 滚动条
        v_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.template_text.yview)
        h_scrollbar = ttk.Scrollbar(text_frame, orient=tk.HORIZONTAL, command=self.template_text.xview)
        
        self.template_text.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.template_text.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        text_frame.grid_rowconfigure(0, weight=1)
        text_frame.grid_columnconfigure(0, weight=1)
        
        # 模板操作按钮
        template_btn_frame = ttk.Frame(template_frame)
        template_btn_frame.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        ttk.Button(template_btn_frame, text="保存模板", command=self.save_template).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(template_btn_frame, text="清空模板", command=self.clear_template).pack(side=tk.LEFT)
        
        # 底部按钮区域
        bottom_frame = ttk.Frame(self.window)
        bottom_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # 数据操作按钮
        ttk.Button(bottom_frame, text="导入数据", command=self.import_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(bottom_frame, text="导出数据", command=self.export_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(bottom_frame, text="保存所有", command=self.save_all).pack(side=tk.LEFT, padx=(0, 20))
        
        # 关闭按钮
        ttk.Button(bottom_frame, text="关闭", command=self.on_closing).pack(side=tk.RIGHT)
    
    def load_data(self):
        """加载数据到界面"""
        # 加载单位列表
        self.unit_listbox.delete(0, tk.END)
        units = self.data_manager.get_units()
        for unit in units:
            self.unit_listbox.insert(tk.END, unit)
        
        # 清空其他列表
        self.req_listbox.delete(0, tk.END)
        self.template_text.delete(1.0, tk.END)
        self.current_info_label.config(text="请选择单位和要求")
    
    def on_unit_select(self, event):
        """单位选择事件"""
        selection = self.unit_listbox.curselection()
        if selection:
            self.current_unit = self.unit_listbox.get(selection[0])
            self.load_requirements()
            self.template_text.delete(1.0, tk.END)
            self.current_requirement = None
            self.update_current_info()
    
    def load_requirements(self):
        """加载当前单位的要求列表"""
        self.req_listbox.delete(0, tk.END)
        if self.current_unit:
            requirements = self.data_manager.get_requirements(self.current_unit)
            for req in requirements:
                self.req_listbox.insert(tk.END, req)
    
    def on_requirement_select(self, event):
        """要求选择事件"""
        selection = self.req_listbox.curselection()
        if selection and self.current_unit:
            self.current_requirement = self.req_listbox.get(selection[0])
            template = self.data_manager.get_template(self.current_unit, self.current_requirement)
            self.template_text.delete(1.0, tk.END)
            self.template_text.insert(1.0, template)
            self.update_current_info()
    
    def update_current_info(self):
        """更新当前编辑信息显示"""
        if self.current_unit and self.current_requirement:
            info = f"{self.current_unit} - {self.current_requirement}"
        elif self.current_unit:
            info = f"{self.current_unit} - 请选择要求"
        else:
            info = "请选择单位和要求"
        self.current_info_label.config(text=info)
    
    def add_unit(self):
        """添加新单位"""
        dialog = SimpleInputDialog(self.window, "添加单位", "请输入单位名称:")
        if dialog.result:
            unit_name = dialog.result.strip()
            if unit_name:
                if self.data_manager.add_unit(unit_name):
                    self.load_data()
                    messagebox.showinfo("成功", f"单位 '{unit_name}' 添加成功！")
                else:
                    messagebox.showerror("错误", "单位名称已存在或无效！")
    
    def rename_unit(self):
        """重命名单位"""
        if not self.current_unit:
            messagebox.showwarning("警告", "请先选择要重命名的单位！")
            return
        
        dialog = SimpleInputDialog(self.window, "重命名单位", "请输入新的单位名称:", self.current_unit)
        if dialog.result:
            new_name = dialog.result.strip()
            if new_name and new_name != self.current_unit:
                if self.data_manager.rename_unit(self.current_unit, new_name):
                    self.load_data()
                    messagebox.showinfo("成功", f"单位重命名成功！")
                else:
                    messagebox.showerror("错误", "新单位名称已存在或无效！")
    
    def delete_unit(self):
        """删除单位"""
        if not self.current_unit:
            messagebox.showwarning("警告", "请先选择要删除的单位！")
            return
        
        if messagebox.askyesno("确认删除", f"确定要删除单位 '{self.current_unit}' 及其所有数据吗？"):
            if self.data_manager.delete_unit(self.current_unit):
                self.current_unit = None
                self.current_requirement = None
                self.load_data()
                messagebox.showinfo("成功", "单位删除成功！")
    
    def add_requirement(self):
        """添加新要求"""
        if not self.current_unit:
            messagebox.showwarning("警告", "请先选择单位！")
            return
        
        dialog = SimpleInputDialog(self.window, "添加激光要求", "请输入激光要求名称:")
        if dialog.result:
            req_name = dialog.result.strip()
            if req_name:
                if self.data_manager.add_requirement(self.current_unit, req_name):
                    self.load_requirements()
                    messagebox.showinfo("成功", f"激光要求 '{req_name}' 添加成功！")
                else:
                    messagebox.showerror("错误", "激光要求名称已存在或无效！")
    
    def delete_requirement(self):
        """删除要求"""
        if not self.current_unit or not self.current_requirement:
            messagebox.showwarning("警告", "请先选择要删除的激光要求！")
            return
        
        if messagebox.askyesno("确认删除", f"确定要删除激光要求 '{self.current_requirement}' 吗？"):
            if self.data_manager.delete_requirement(self.current_unit, self.current_requirement):
                self.current_requirement = None
                self.load_requirements()
                self.template_text.delete(1.0, tk.END)
                self.update_current_info()
                messagebox.showinfo("成功", "激光要求删除成功！")
    
    def save_template(self):
        """保存模板代码"""
        if not self.current_unit or not self.current_requirement:
            messagebox.showwarning("警告", "请先选择单位和激光要求！")
            return
        
        template = self.template_text.get(1.0, tk.END).strip()
        if self.data_manager.update_template(self.current_unit, self.current_requirement, template):
            messagebox.showinfo("成功", "模板代码保存成功！")
        else:
            messagebox.showerror("错误", "模板代码保存失败！")
    
    def clear_template(self):
        """清空模板代码"""
        if messagebox.askyesno("确认清空", "确定要清空当前模板代码吗？"):
            self.template_text.delete(1.0, tk.END)
    
    def import_data(self):
        """导入数据"""
        file_path = filedialog.askopenfilename(
            title="选择要导入的数据文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if file_path:
            if self.data_manager.import_data(file_path):
                self.load_data()
                messagebox.showinfo("成功", "数据导入成功！")
            else:
                messagebox.showerror("错误", "数据导入失败！请检查文件格式。")
    
    def export_data(self):
        """导出数据"""
        file_path = filedialog.asksaveasfilename(
            title="选择导出位置",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if file_path:
            if self.data_manager.export_data(file_path):
                messagebox.showinfo("成功", "数据导出成功！")
            else:
                messagebox.showerror("错误", "数据导出失败！")
    
    def save_all(self):
        """保存所有数据"""
        if self.data_manager.save_data():
            messagebox.showinfo("成功", "所有数据保存成功！")
            if self.callback:
                self.callback()
        else:
            messagebox.showerror("错误", "数据保存失败！")
    
    def on_closing(self):
        """窗口关闭事件"""
        if messagebox.askyesno("确认关闭", "是否保存数据并关闭设置窗口？"):
            self.save_all()
            self.window.destroy()


class SimpleInputDialog:
    """简单输入对话框"""
    def __init__(self, parent, title, prompt, default_value=""):
        self.result = None
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("300x120")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        # 创建组件
        ttk.Label(self.dialog, text=prompt).pack(pady=10)
        
        self.entry = ttk.Entry(self.dialog, width=30)
        self.entry.pack(pady=5)
        self.entry.insert(0, default_value)
        self.entry.select_range(0, tk.END)
        self.entry.focus()
        
        # 按钮框架
        btn_frame = ttk.Frame(self.dialog)
        btn_frame.pack(pady=10)
        
        ttk.Button(btn_frame, text="确定", command=self.ok_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="取消", command=self.cancel_clicked).pack(side=tk.LEFT, padx=5)
        
        # 绑定回车键
        self.entry.bind('<Return>', lambda e: self.ok_clicked())
        self.dialog.bind('<Escape>', lambda e: self.cancel_clicked())
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def ok_clicked(self):
        """确定按钮点击"""
        self.result = self.entry.get()
        self.dialog.destroy()
    
    def cancel_clicked(self):
        """取消按钮点击"""
        self.result = None
        self.dialog.destroy()
