# -*- coding: utf-8 -*-
"""
激光模板查询器主程序
提供三级查询功能：单位 -> 激光要求 -> 模板代码
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_manager import DataManager
from settings_window import SettingsWindow


class LaserTemplateQueryApp:
    def __init__(self):
        """初始化激光模板查询器应用"""
        self.root = tk.Tk()
        self.root.title("🔥 激光模板查询器 v1.0")
        self.root.geometry("750x480")
        self.root.minsize(650, 400)
        
        # 初始化数据管理器
        self.data_manager = DataManager()

        # 窗口置顶状态
        self.always_on_top = tk.BooleanVar(value=False)

        # 当前选中的数据
        self.current_unit = None
        self.current_requirement = None

        # 配置现代化样式
        self.setup_modern_style()

        # 创建界面
        self.create_widgets()
        self.load_units()

        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_modern_style(self):
        """配置现代化界面样式"""
        # 配置升级版主题色彩 - 更加和谐的配色方案
        self.colors = {
            # 主色调 - 使用更柔和的蓝紫色渐变
            'primary': '#6366f1',           # 靛蓝色主色调
            'primary_light': '#818cf8',     # 浅靛蓝色
            'primary_dark': '#4f46e5',      # 深靛蓝色
            'primary_hover': '#7c3aed',     # 悬停紫色

            # 辅助色调
            'secondary': '#64748b',         # 石板灰
            'secondary_light': '#94a3b8',   # 浅石板灰
            'accent': '#06b6d4',            # 青色强调色
            'accent_light': '#22d3ee',      # 浅青色

            # 状态色彩
            'success': '#10b981',           # 翠绿色
            'success_light': '#34d399',     # 浅翠绿色
            'warning': '#f59e0b',           # 琥珀色
            'warning_light': '#fbbf24',     # 浅琥珀色
            'danger': '#ef4444',            # 红色
            'danger_light': '#f87171',      # 浅红色

            # 背景色彩 - 渐变和层次
            'background': '#f1f5f9',        # 主背景 - 浅蓝灰色
            'background_dark': '#e2e8f0',   # 深背景
            'surface': '#ffffff',           # 表面白色
            'surface_hover': '#f8fafc',     # 悬停表面色
            'card': '#ffffff',              # 卡片背景
            'card_hover': '#f8fafc',        # 卡片悬停

            # 文字色彩
            'text': '#0f172a',              # 主文字 - 深蓝灰色
            'text_secondary': '#475569',    # 次要文字
            'text_muted': '#94a3b8',        # 静音文字
            'text_inverse': '#ffffff',      # 反色文字

            # 边框色彩
            'border': '#e2e8f0',            # 默认边框
            'border_focus': '#6366f1',      # 焦点边框
            'border_hover': '#cbd5e1',      # 悬停边框

            # 阴影色彩
            'shadow_light': 'rgba(99, 102, 241, 0.1)',   # 浅阴影
            'shadow_medium': 'rgba(99, 102, 241, 0.2)',  # 中等阴影
            'shadow_dark': 'rgba(15, 23, 42, 0.1)',      # 深阴影
        }

        # 升级版Unicode图标 - 更美观和统一的图标系统
        self.icons = {
            # 主要操作图标
            'refresh': '🔄',
            'settings': '⚙️',
            'copy': '📋',
            'save': '💾',
            'clear': '🗑️',
            'export': '📤',
            'import': '📥',

            # 编辑操作图标
            'add': '➕',
            'delete': '🗑️',
            'edit': '✏️',
            'rename': '📝',

            # 导航和状态图标
            'search': '🔍',
            'pin': '📌',
            'folder': '📁',
            'file': '📄',
            'check': '✅',
            'warning': '⚠️',
            'error': '❌',
            'info': 'ℹ️',

            # 状态指示图标
            'ready': '🟢',
            'loading': '🔄',
            'success': '✅',
            'failed': '❌',

            # 功能图标
            'laser': '🔥',
            'template': '📋',
            'unit': '🏢',
            'requirement': '📋',
            'code': '💻',

            # 装饰图标
            'star': '⭐',
            'heart': '💙',
            'diamond': '💎',
            'sparkle': '✨'
        }

        # 配置高级ttk样式系统
        self.style = ttk.Style()

        # 配置现代化按钮样式
        self.style.configure('Modern.TButton',
                           padding=(14, 10),
                           font=('微软雅黑', 9),
                           relief='flat',
                           borderwidth=0,
                           focuscolor='none')

        # 主要按钮样式 - 带渐变效果
        self.style.configure('Primary.TButton',
                           padding=(16, 12),
                           font=('微软雅黑', 9, 'bold'),
                           relief='flat',
                           borderwidth=0,
                           focuscolor='none')

        # 成功按钮样式
        self.style.configure('Success.TButton',
                           padding=(14, 10),
                           font=('微软雅黑', 9),
                           relief='flat',
                           borderwidth=0,
                           focuscolor='none')

        # 危险按钮样式
        self.style.configure('Danger.TButton',
                           padding=(14, 10),
                           font=('微软雅黑', 9),
                           relief='flat',
                           borderwidth=0,
                           focuscolor='none')

        # 配置现代化标签框样式
        self.style.configure('Modern.TLabelframe',
                           padding=12,
                           relief='flat',
                           borderwidth=1,
                           background=self.colors['surface'])

        self.style.configure('Modern.TLabelframe.Label',
                           font=('微软雅黑', 10, 'bold'),
                           foreground=self.colors['primary'],
                           background=self.colors['surface'])

        # 配置组合框样式
        self.style.configure('Modern.TCombobox',
                           padding=8,
                           font=('微软雅黑', 9),
                           relief='flat',
                           borderwidth=1,
                           focuscolor='none')

        # 配置复选框样式
        self.style.configure('Modern.TCheckbutton',
                           font=('微软雅黑', 9),
                           focuscolor='none',
                           background=self.colors['background'])

        # 设置根窗口背景色 - 渐变效果
        self.root.configure(bg=self.colors['background'])

        # 创建自定义样式映射（悬停效果）
        self.setup_hover_effects()

    def setup_hover_effects(self):
        """配置悬停效果和状态映射"""
        # 按钮悬停效果映射
        self.style.map('Modern.TButton',
                      background=[('active', self.colors['surface_hover']),
                                ('pressed', self.colors['background_dark'])],
                      foreground=[('active', self.colors['primary']),
                                ('pressed', self.colors['primary_dark'])],
                      relief=[('pressed', 'flat'),
                            ('!pressed', 'flat')])

        self.style.map('Primary.TButton',
                      background=[('active', self.colors['primary_hover']),
                                ('pressed', self.colors['primary_dark'])],
                      foreground=[('active', self.colors['text_inverse']),
                                ('pressed', self.colors['text_inverse'])],
                      relief=[('pressed', 'flat'),
                            ('!pressed', 'flat')])

        self.style.map('Success.TButton',
                      background=[('active', self.colors['success_light']),
                                ('pressed', self.colors['success'])],
                      foreground=[('active', self.colors['text_inverse']),
                                ('pressed', self.colors['text_inverse'])])

        self.style.map('Danger.TButton',
                      background=[('active', self.colors['danger_light']),
                                ('pressed', self.colors['danger'])],
                      foreground=[('active', self.colors['text_inverse']),
                                ('pressed', self.colors['text_inverse'])])

        # 组合框悬停效果
        self.style.map('Modern.TCombobox',
                      fieldbackground=[('readonly', self.colors['surface']),
                                     ('focus', self.colors['surface_hover'])],
                      bordercolor=[('focus', self.colors['border_focus']),
                                 ('!focus', self.colors['border'])],
                      arrowcolor=[('active', self.colors['primary']),
                                ('!active', self.colors['secondary'])])

        # 复选框悬停效果
        self.style.map('Modern.TCheckbutton',
                      background=[('active', self.colors['background']),
                                ('!active', self.colors['background'])],
                      foreground=[('active', self.colors['primary']),
                                ('!active', self.colors['text'])])

    def create_gradient_frame(self, parent, color1, color2):
        """创建渐变背景框架（模拟效果）"""
        frame = tk.Frame(parent, bg=color1, relief='flat', bd=0)
        return frame

    def add_button_hover_effect(self, button):
        """为按钮添加悬停效果"""
        def on_enter(event):
            button.configure(cursor='hand2')

        def on_leave(event):
            button.configure(cursor='')

        button.bind('<Enter>', on_enter)
        button.bind('<Leave>', on_leave)

    def create_modern_listbox(self, parent, **kwargs):
        """创建现代化列表框"""
        # 创建容器框架
        container = tk.Frame(parent, bg=self.colors['surface'], relief='flat', bd=1)

        # 创建列表框
        listbox = tk.Listbox(
            container,
            font=("微软雅黑", 9),
            selectbackground=self.colors['primary'],
            selectforeground=self.colors['text_inverse'],
            bg=self.colors['surface'],
            fg=self.colors['text'],
            relief='flat',
            borderwidth=0,
            highlightthickness=0,
            activestyle='none',
            **kwargs
        )

        # 创建滚动条
        scrollbar = ttk.Scrollbar(container, orient=tk.VERTICAL, command=listbox.yview)
        listbox.configure(yscrollcommand=scrollbar.set)

        # 布局
        listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=2, pady=2)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        return container, listbox
    
    def create_widgets(self):
        """创建主界面组件"""
        # 创建菜单栏
        self.create_menu()

        # 主框架 - 使用渐变背景
        main_frame = self.create_gradient_frame(self.root, self.colors['background'], self.colors['surface'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=12, pady=12)

        # 顶部控制面板 - 美化版
        control_frame = tk.Frame(main_frame, bg=self.colors['card'], relief='flat', bd=1)
        control_frame.pack(fill=tk.X, pady=(0, 12), padx=2)

        # 添加内边距
        control_inner = tk.Frame(control_frame, bg=self.colors['card'])
        control_inner.pack(fill=tk.X, padx=16, pady=12)

        # 左侧控制组
        left_controls = tk.Frame(control_inner, bg=self.colors['card'])
        left_controls.pack(side=tk.LEFT)

        # 窗口置顶复选框（美化版）
        self.topmost_check = ttk.Checkbutton(
            left_controls,
            text=f"{self.icons['pin']} 窗口置顶",
            variable=self.always_on_top,
            command=self.toggle_topmost,
            style='Modern.TCheckbutton'
        )
        self.topmost_check.pack(side=tk.LEFT)

        # 刷新按钮（美化版）
        refresh_btn = ttk.Button(
            left_controls,
            text=f"{self.icons['refresh']} 刷新",
            command=self.refresh_data,
            style='Modern.TButton'
        )
        refresh_btn.pack(side=tk.LEFT, padx=(12, 0))

        # 右侧控制组
        right_controls = tk.Frame(control_inner, bg=self.colors['card'])
        right_controls.pack(side=tk.RIGHT)

        # 设置按钮（美化版主要按钮）
        settings_btn = ttk.Button(
            right_controls,
            text=f"{self.icons['settings']} 数据设置",
            command=self.open_settings,
            style='Primary.TButton'
        )
        settings_btn.pack(side=tk.RIGHT)

        # 为按钮添加悬停效果
        self.add_button_hover_effect(refresh_btn)
        self.add_button_hover_effect(settings_btn)
        
        # 创建三级查询面板
        query_frame = tk.Frame(main_frame, bg=self.colors['background'])
        query_frame.pack(fill=tk.BOTH, expand=True)

        # 第一级：单位选择 - 美化版
        unit_frame = ttk.LabelFrame(
            query_frame,
            text=f"{self.icons['unit']} 第一步：选择单位",
            style='Modern.TLabelframe'
        )
        unit_frame.pack(fill=tk.X, pady=(0, 8), padx=2)

        # 单位选择方式框架
        unit_select_frame = tk.Frame(unit_frame, bg=self.colors['surface'])
        unit_select_frame.pack(fill=tk.X, padx=12, pady=10)

        # 下拉列表方式
        dropdown_frame = tk.Frame(unit_select_frame, bg=self.colors['surface'])
        dropdown_frame.pack(side=tk.LEFT)

        ttk.Label(
            dropdown_frame,
            text="下拉选择:",
            font=('微软雅黑', 9, 'bold'),
            background=self.colors['surface'],
            foreground=self.colors['text_secondary']
        ).pack(side=tk.LEFT)

        self.unit_combobox = ttk.Combobox(
            dropdown_frame,
            state="readonly",
            width=18,
            style='Modern.TCombobox',
            font=('微软雅黑', 9)
        )
        self.unit_combobox.pack(side=tk.LEFT, padx=(8, 0))
        self.unit_combobox.bind('<<ComboboxSelected>>', self.on_unit_selected)

        # 分隔线
        separator = tk.Frame(unit_select_frame, width=2, bg=self.colors['border'])
        separator.pack(side=tk.LEFT, fill=tk.Y, padx=20)

        # 按钮选择方式
        buttons_section = tk.Frame(unit_select_frame, bg=self.colors['surface'])
        buttons_section.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Label(
            buttons_section,
            text="快速选择:",
            font=('微软雅黑', 9, 'bold'),
            background=self.colors['surface'],
            foreground=self.colors['text_secondary']
        ).pack(side=tk.LEFT)

        self.unit_buttons_frame = tk.Frame(buttons_section, bg=self.colors['surface'])
        self.unit_buttons_frame.pack(side=tk.LEFT, padx=(8, 0), fill=tk.X, expand=True)
        
        # 第二级：激光要求选择 - 美化版
        req_frame = ttk.LabelFrame(
            query_frame,
            text=f"{self.icons['requirement']} 第二步：选择激光要求",
            style='Modern.TLabelframe'
        )
        req_frame.pack(fill=tk.X, pady=(0, 8), padx=2)

        # 要求列表框架
        req_list_container = tk.Frame(req_frame, bg=self.colors['surface'])
        req_list_container.pack(fill=tk.X, padx=12, pady=10)

        # 使用现代化列表框
        self.req_listbox_container, self.req_listbox = self.create_modern_listbox(
            req_list_container,
            height=4
        )
        self.req_listbox_container.pack(fill=tk.BOTH, expand=True)

        # 绑定事件
        self.req_listbox.bind('<<ListboxSelect>>', self.on_requirement_selected)
        self.req_listbox.bind('<Double-Button-1>', self.on_requirement_double_click)

        # 添加提示文本
        hint_label = ttk.Label(
            req_list_container,
            text="💡 提示：双击激光要求可直接复制模板代码",
            font=('微软雅黑', 8),
            foreground=self.colors['text_muted'],
            background=self.colors['surface']
        )
        hint_label.pack(pady=(4, 0))
        
        # 第三级：模板代码显示 - 美化版
        template_frame = ttk.LabelFrame(
            query_frame,
            text=f"{self.icons['code']} 第三步：激光模板代码",
            style='Modern.TLabelframe'
        )
        template_frame.pack(fill=tk.BOTH, expand=True, padx=2)

        # 模板信息显示 - 美化版
        info_frame = tk.Frame(template_frame, bg=self.colors['surface'])
        info_frame.pack(fill=tk.X, padx=12, pady=(10, 6))

        # 状态指示器
        status_frame = tk.Frame(info_frame, bg=self.colors['surface'])
        status_frame.pack(side=tk.LEFT)

        self.status_indicator = ttk.Label(
            status_frame,
            text=self.icons['ready'],
            font=('微软雅黑', 10),
            background=self.colors['surface']
        )
        self.status_indicator.pack(side=tk.LEFT)

        ttk.Label(
            status_frame,
            text="当前模板:",
            font=('微软雅黑', 9, 'bold'),
            background=self.colors['surface'],
            foreground=self.colors['text']
        ).pack(side=tk.LEFT, padx=(4, 0))

        self.template_info_label = ttk.Label(
            info_frame,
            text="请选择单位和激光要求",
            foreground=self.colors['text_secondary'],
            font=('微软雅黑', 9),
            background=self.colors['surface']
        )
        self.template_info_label.pack(side=tk.LEFT, padx=(8, 0))

        # 模板代码显示区域 - 美化版
        text_container = tk.Frame(template_frame, bg=self.colors['surface'])
        text_container.pack(fill=tk.X, padx=12, pady=(0, 8))

        # 代码显示框架 - 带阴影效果
        code_frame = tk.Frame(
            text_container,
            bg=self.colors['card'],
            relief='flat',
            bd=1,
            highlightbackground=self.colors['border'],
            highlightthickness=1
        )
        code_frame.pack(fill=tk.X, pady=4)

        # 代码内容框架
        code_inner = tk.Frame(code_frame, bg=self.colors['card'])
        code_inner.pack(fill=tk.X, padx=8, pady=8)

        # 单行代码显示（Entry）
        self.template_var = tk.StringVar()
        self.template_entry = tk.Entry(
            code_inner,
            textvariable=self.template_var,
            font=("Consolas", 10),
            bg=self.colors['card'],
            fg=self.colors['text'],
            relief='flat',
            borderwidth=0,
            highlightthickness=0,
            state='readonly',
            readonlybackground=self.colors['card']
        )
        self.template_entry.pack(fill=tk.X, pady=2)

        # 多行代码显示（Text）
        self.template_text = tk.Text(
            code_inner,
            height=3,
            wrap=tk.WORD,
            font=("Consolas", 9),
            bg=self.colors['card'],
            fg=self.colors['text'],
            selectbackground=self.colors['primary'],
            selectforeground=self.colors['text_inverse'],
            relief='flat',
            borderwidth=0,
            highlightthickness=0,
            state='disabled'
        )
        # 默认隐藏多行文本框
        
        # 模板操作按钮 - 美化版
        btn_container = tk.Frame(template_frame, bg=self.colors['surface'])
        btn_container.pack(fill=tk.X, padx=12, pady=(4, 10))

        # 按钮框架 - 带背景色
        template_btn_frame = tk.Frame(btn_container, bg=self.colors['background_dark'], relief='flat', bd=1)
        template_btn_frame.pack(fill=tk.X, pady=2)

        # 内部按钮容器
        btn_inner = tk.Frame(template_btn_frame, bg=self.colors['background_dark'])
        btn_inner.pack(fill=tk.X, padx=12, pady=8)

        # 左侧按钮组
        left_btn_group = tk.Frame(btn_inner, bg=self.colors['background_dark'])
        left_btn_group.pack(side=tk.LEFT)

        # 复制按钮 - 成功样式
        copy_btn = ttk.Button(
            left_btn_group,
            text=f"{self.icons['copy']} 复制",
            command=self.copy_template,
            style='Success.TButton'
        )
        copy_btn.pack(side=tk.LEFT, padx=(0, 6))

        # 清空按钮 - 普通样式
        clear_btn = ttk.Button(
            left_btn_group,
            text=f"{self.icons['clear']} 清空",
            command=self.clear_template_display,
            style='Modern.TButton'
        )
        clear_btn.pack(side=tk.LEFT, padx=(0, 6))

        # 右侧按钮组
        right_btn_group = tk.Frame(btn_inner, bg=self.colors['background_dark'])
        right_btn_group.pack(side=tk.RIGHT)

        # 保存按钮 - 主要样式
        save_btn = ttk.Button(
            right_btn_group,
            text=f"{self.icons['save']} 保存到文件",
            command=self.save_template_to_file,
            style='Primary.TButton'
        )
        save_btn.pack(side=tk.RIGHT)

        # 为所有按钮添加悬停效果
        for btn in [copy_btn, clear_btn, save_btn]:
            self.add_button_hover_effect(btn)

        # 现代化状态栏 - 美化版
        status_container = tk.Frame(self.root, bg=self.colors['background_dark'], height=32)
        status_container.pack(side=tk.BOTTOM, fill=tk.X)
        status_container.pack_propagate(False)

        # 状态栏内容
        status_inner = tk.Frame(status_container, bg=self.colors['background_dark'])
        status_inner.pack(fill=tk.BOTH, expand=True, padx=12, pady=6)

        # 左侧状态信息
        status_left = tk.Frame(status_inner, bg=self.colors['background_dark'])
        status_left.pack(side=tk.LEFT)

        self.status_bar = tk.Label(
            status_left,
            text=f"{self.icons['ready']} 就绪",
            font=('微软雅黑', 9),
            fg=self.colors['text_secondary'],
            bg=self.colors['background_dark']
        )
        self.status_bar.pack(side=tk.LEFT)

        # 右侧信息显示
        status_right = tk.Frame(status_inner, bg=self.colors['background_dark'])
        status_right.pack(side=tk.RIGHT)

        # 版本信息
        version_label = tk.Label(
            status_right,
            text=f"{self.icons['sparkle']} v1.1",
            font=('微软雅黑', 8),
            fg=self.colors['text_muted'],
            bg=self.colors['background_dark']
        )
        version_label.pack(side=tk.RIGHT)
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="刷新数据", command=self.refresh_data)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 设置菜单
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="设置", menu=settings_menu)
        settings_menu.add_command(label="数据管理", command=self.open_settings)
        settings_menu.add_checkbutton(label="窗口置顶", variable=self.always_on_top, command=self.toggle_topmost)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def load_units(self):
        """加载单位数据"""
        units = self.data_manager.get_units()

        # 更新下拉列表
        self.unit_combobox['values'] = units
        if units:
            self.unit_combobox.set('')

        # 更新快速选择按钮
        for widget in self.unit_buttons_frame.winfo_children():
            widget.destroy()

        for i, unit in enumerate(units):
            btn = ttk.Button(
                self.unit_buttons_frame,
                text=unit,
                command=lambda u=unit: self.select_unit(u),
                width=10,
                style='Modern.TButton'
            )
            btn.pack(side=tk.LEFT, padx=3, pady=2)

            # 为每个按钮添加悬停效果
            self.add_button_hover_effect(btn)

            # 每4个按钮换行，更紧凑美观
            if i > 0 and (i + 1) % 4 == 0:
                tk.Frame(self.unit_buttons_frame, height=4, bg=self.colors['surface']).pack()

        # 清空其他显示
        self.req_listbox.delete(0, tk.END)
        self.template_var.set("")
        self.template_text.config(state='normal')
        self.template_text.delete(1.0, tk.END)
        self.template_text.config(state='disabled')
        self.update_status(f"📊 已加载 {len(units)} 个单位")
    
    def on_unit_selected(self, event):
        """下拉列表单位选择事件"""
        unit = self.unit_combobox.get()
        if unit:
            self.select_unit(unit)
    
    def select_unit(self, unit):
        """选择单位"""
        self.current_unit = unit
        self.current_requirement = None

        # 更新下拉列表显示
        self.unit_combobox.set(unit)

        # 加载激光要求
        self.load_requirements()

        # 清空模板显示
        self.template_var.set("")
        self.template_text.config(state='normal')
        self.template_text.delete(1.0, tk.END)
        self.template_text.config(state='disabled')
        self.update_template_info()

        self.update_status(f"🏢 已选择单位: {unit}")

    def load_requirements(self):
        """加载当前单位的激光要求"""
        self.req_listbox.delete(0, tk.END)

        if self.current_unit:
            requirements = self.data_manager.get_requirements(self.current_unit)
            for req in requirements:
                self.req_listbox.insert(tk.END, req)

            if requirements:
                self.update_status(f"📋 单位 '{self.current_unit}' 有 {len(requirements)} 个激光要求")
            else:
                self.update_status(f"⚠️ 单位 '{self.current_unit}' 暂无激光要求")
    
    def on_requirement_selected(self, event):
        """激光要求选择事件"""
        selection = self.req_listbox.curselection()
        if selection and self.current_unit:
            requirement = self.req_listbox.get(selection[0])
            self.select_requirement(requirement)
    
    def on_requirement_double_click(self, event):
        """激光要求双击事件"""
        self.on_requirement_selected(event)
        if self.current_requirement:
            self.copy_template()
    
    def select_requirement(self, requirement):
        """选择激光要求"""
        self.current_requirement = requirement

        # 加载模板代码
        template = self.data_manager.get_template(self.current_unit, requirement)

        # 判断是单行还是多行代码
        if '\n' in template and len(template.split('\n')) > 1:
            # 多行代码，显示Text组件
            self.template_entry.pack_forget()
            self.template_text.pack(fill=tk.X, pady=2)
            self.template_text.config(state='normal')
            self.template_text.delete(1.0, tk.END)
            self.template_text.insert(1.0, template)
            self.template_text.config(state='disabled')
        else:
            # 单行代码，显示Entry组件
            self.template_text.pack_forget()
            self.template_entry.pack(fill=tk.X, pady=2)
            self.template_var.set(template)

        self.update_template_info()
        self.update_status(f"✅ 已加载模板: {self.current_unit} - {requirement}")

    def update_template_info(self):
        """更新模板信息显示 - 美化版"""
        if self.current_unit and self.current_requirement:
            info = f"{self.current_unit} - {self.current_requirement}"
            self.template_info_label.config(text=info, foreground=self.colors['text'])
            self.status_indicator.config(text=self.icons['success'])
        elif self.current_unit:
            info = f"{self.current_unit} - 请选择激光要求"
            self.template_info_label.config(text=info, foreground=self.colors['text_secondary'])
            self.status_indicator.config(text=self.icons['warning'])
        else:
            self.template_info_label.config(text="请选择单位和激光要求", foreground=self.colors['text_secondary'])
            self.status_indicator.config(text=self.icons['ready'])
    
    def copy_template(self):
        """复制模板代码到剪贴板"""
        # 获取当前显示的模板代码
        if self.template_entry.winfo_viewable():
            template = self.template_var.get().strip()
        else:
            template = self.template_text.get(1.0, tk.END).strip()

        if template:
            self.root.clipboard_clear()
            self.root.clipboard_append(template)
            self.update_status(f"{self.icons['check']} 模板代码已复制到剪贴板")
            messagebox.showinfo("复制成功", f"{self.icons['check']} 模板代码已复制到剪贴板！")
        else:
            messagebox.showwarning("警告", "没有可复制的模板代码！")

    def clear_template_display(self):
        """清空模板显示"""
        self.template_var.set("")
        self.template_text.config(state='normal')
        self.template_text.delete(1.0, tk.END)
        self.template_text.config(state='disabled')
        self.current_requirement = None
        self.update_template_info()
        self.update_status("🗑️ 已清空模板显示")
    
    def save_template_to_file(self):
        """保存模板代码到文件"""
        # 获取当前显示的模板代码
        if self.template_entry.winfo_viewable():
            template = self.template_var.get().strip()
        else:
            template = self.template_text.get(1.0, tk.END).strip()

        if not template:
            messagebox.showwarning("警告", "没有可保存的模板代码！")
            return

        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            title="保存模板代码",
            defaultextension=".gcode",
            filetypes=[("G代码文件", "*.gcode"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(template)
                messagebox.showinfo("保存成功", f"{self.icons['check']} 模板代码已保存到:\n{filename}")
                self.update_status(f"{self.icons['save']} 模板已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("保存失败", f"保存文件时出错: {e}")
    
    def toggle_topmost(self):
        """切换窗口置顶状态"""
        self.root.attributes('-topmost', self.always_on_top.get())
        status = "开启" if self.always_on_top.get() else "关闭"
        icon = self.icons['pin'] if self.always_on_top.get() else "📌"
        self.update_status(f"{icon} 窗口置顶已{status}")

    def refresh_data(self):
        """刷新数据"""
        self.data_manager.load_data()
        self.load_units()
        self.current_unit = None
        self.current_requirement = None
        self.update_template_info()
        messagebox.showinfo("刷新完成", f"{self.icons['check']} 数据已刷新！")
    
    def open_settings(self):
        """打开设置窗口"""
        SettingsWindow(self.root, self.data_manager, self.refresh_data)
    
    def show_help(self):
        """显示使用说明"""
        help_text = """激光模板查询器使用说明：

1. 选择单位：
   - 使用下拉列表选择单位
   - 或点击快速选择按钮

2. 选择激光要求：
   - 在激光要求列表中点击选择
   - 双击可直接复制模板代码

3. 查看模板代码：
   - 选择要求后自动显示对应的模板代码
   - 可复制代码到剪贴板
   - 可保存代码到文件

4. 数据管理：
   - 点击"数据设置"按钮进入管理界面
   - 可添加、编辑、删除单位和要求
   - 支持数据导入导出

5. 其他功能：
   - 窗口置顶：保持窗口在最前面
   - 刷新数据：重新加载数据文件"""
        
        messagebox.showinfo("使用说明", help_text)
    
    def show_about(self):
        """显示关于信息"""
        about_text = """激光模板查询器 v1.0

一个用于快速查询激光加工模板代码的桌面应用程序。

功能特点：
• 三级查询：单位 → 激光要求 → 模板代码
• 数据管理：支持添加、编辑、删除数据
• 数据持久化：JSON格式存储
• 导入导出：支持数据备份和恢复
• 窗口置顶：方便与其他软件配合使用

开发语言：Python + tkinter
版本：1.0
"""
        messagebox.showinfo("关于", about_text)
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_bar.config(text=message)
    
    def on_closing(self):
        """程序关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出激光模板查询器吗？"):
            self.root.destroy()
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = LaserTemplateQueryApp()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {e}")
        print(f"错误详情: {e}")


if __name__ == "__main__":
    main()
