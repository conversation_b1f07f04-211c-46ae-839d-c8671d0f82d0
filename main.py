# -*- coding: utf-8 -*-
"""
激光模板查询器主程序
提供三级查询功能：单位 -> 激光要求 -> 模板代码
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_manager import DataManager
from settings_window import SettingsWindow


class LaserTemplateQueryApp:
    def __init__(self):
        """初始化激光模板查询器应用"""
        self.root = tk.Tk()
        self.root.title("🔥 激光模板查询器 v1.0")
        self.root.geometry("750x480")
        self.root.minsize(650, 400)
        
        # 初始化数据管理器
        self.data_manager = DataManager()

        # 窗口置顶状态
        self.always_on_top = tk.BooleanVar(value=False)

        # 当前选中的数据
        self.current_unit = None
        self.current_requirement = None

        # 配置现代化样式
        self.setup_modern_style()

        # 创建界面
        self.create_widgets()
        self.load_units()

        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_modern_style(self):
        """配置现代化界面样式"""
        # 配置主题色彩
        self.colors = {
            'primary': '#2563eb',      # 蓝色主色调
            'primary_light': '#3b82f6',
            'secondary': '#64748b',    # 灰色辅助色
            'success': '#10b981',      # 绿色
            'warning': '#f59e0b',      # 橙色
            'danger': '#ef4444',       # 红色
            'background': '#f8fafc',   # 浅灰背景
            'surface': '#ffffff',      # 白色表面
            'text': '#1e293b',         # 深色文字
            'text_secondary': '#64748b' # 次要文字
        }

        # Unicode图标
        self.icons = {
            'refresh': '🔄',
            'settings': '⚙️',
            'copy': '📋',
            'save': '💾',
            'clear': '🗑️',
            'export': '📤',
            'import': '📥',
            'add': '➕',
            'delete': '❌',
            'edit': '✏️',
            'search': '🔍',
            'pin': '📌',
            'folder': '📁',
            'file': '📄',
            'check': '✅'
        }

        # 配置ttk样式
        style = ttk.Style()

        # 配置按钮样式
        style.configure('Modern.TButton',
                       padding=(12, 8),
                       font=('微软雅黑', 9))

        style.configure('Primary.TButton',
                       padding=(12, 8),
                       font=('微软雅黑', 9, 'bold'))

        # 配置标签框样式
        style.configure('Modern.TLabelframe',
                       padding=8,
                       relief='flat',
                       borderwidth=1)

        style.configure('Modern.TLabelframe.Label',
                       font=('微软雅黑', 9, 'bold'),
                       foreground=self.colors['primary'])

        # 配置组合框样式
        style.configure('Modern.TCombobox',
                       padding=5,
                       font=('微软雅黑', 9))

        # 设置根窗口背景色
        self.root.configure(bg=self.colors['background'])
    
    def create_widgets(self):
        """创建主界面组件"""
        # 创建菜单栏
        self.create_menu()

        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=8, pady=8)

        # 顶部控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 8))

        # 左侧控制组
        left_controls = ttk.Frame(control_frame)
        left_controls.pack(side=tk.LEFT)

        # 窗口置顶复选框（带图标）
        self.topmost_check = ttk.Checkbutton(
            left_controls,
            text=f"{self.icons['pin']} 窗口置顶",
            variable=self.always_on_top,
            command=self.toggle_topmost
        )
        self.topmost_check.pack(side=tk.LEFT)

        # 刷新按钮
        refresh_btn = ttk.Button(
            left_controls,
            text=f"{self.icons['refresh']} 刷新",
            command=self.refresh_data,
            style='Modern.TButton'
        )
        refresh_btn.pack(side=tk.LEFT, padx=(8, 0))

        # 右侧控制组
        right_controls = ttk.Frame(control_frame)
        right_controls.pack(side=tk.RIGHT)

        # 设置按钮
        settings_btn = ttk.Button(
            right_controls,
            text=f"{self.icons['settings']} 数据设置",
            command=self.open_settings,
            style='Primary.TButton'
        )
        settings_btn.pack(side=tk.RIGHT)
        
        # 创建三级查询面板
        query_frame = ttk.Frame(main_frame)
        query_frame.pack(fill=tk.BOTH, expand=True)

        # 第一级：单位选择
        unit_frame = ttk.LabelFrame(query_frame, text=f"{self.icons['folder']} 第一步：选择单位", style='Modern.TLabelframe')
        unit_frame.pack(fill=tk.X, pady=(0, 6))

        # 单位选择方式框架
        unit_select_frame = ttk.Frame(unit_frame)
        unit_select_frame.pack(fill=tk.X, padx=8, pady=6)

        # 下拉列表方式
        ttk.Label(unit_select_frame, text="下拉选择:", font=('微软雅黑', 9)).pack(side=tk.LEFT)
        self.unit_combobox = ttk.Combobox(
            unit_select_frame,
            state="readonly",
            width=18,
            style='Modern.TCombobox',
            font=('微软雅黑', 9)
        )
        self.unit_combobox.pack(side=tk.LEFT, padx=(5, 15))
        self.unit_combobox.bind('<<ComboboxSelected>>', self.on_unit_selected)

        # 按钮选择方式
        ttk.Label(unit_select_frame, text="快速选择:", font=('微软雅黑', 9)).pack(side=tk.LEFT)
        self.unit_buttons_frame = ttk.Frame(unit_select_frame)
        self.unit_buttons_frame.pack(side=tk.LEFT, padx=(5, 0), fill=tk.X, expand=True)
        
        # 第二级：激光要求选择
        req_frame = ttk.LabelFrame(query_frame, text=f"{self.icons['search']} 第二步：选择激光要求", style='Modern.TLabelframe')
        req_frame.pack(fill=tk.X, pady=(0, 6))

        # 要求列表框架
        req_list_frame = ttk.Frame(req_frame)
        req_list_frame.pack(fill=tk.X, padx=8, pady=6)

        # 要求列表（使用Listbox显示，减小高度）
        self.req_listbox = tk.Listbox(
            req_list_frame,
            height=4,  # 减小高度从6到4
            font=("微软雅黑", 9),
            selectbackground=self.colors['primary'],
            selectforeground='white',
            bg=self.colors['surface'],
            fg=self.colors['text'],
            relief='flat',
            borderwidth=1,
            highlightthickness=1,
            highlightcolor=self.colors['primary']
        )
        req_scrollbar = ttk.Scrollbar(req_list_frame, orient=tk.VERTICAL, command=self.req_listbox.yview)
        self.req_listbox.configure(yscrollcommand=req_scrollbar.set)

        self.req_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        req_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.req_listbox.bind('<<ListboxSelect>>', self.on_requirement_selected)
        self.req_listbox.bind('<Double-Button-1>', self.on_requirement_double_click)
        
        # 第三级：模板代码显示
        template_frame = ttk.LabelFrame(query_frame, text=f"{self.icons['file']} 第三步：激光模板代码", style='Modern.TLabelframe')
        template_frame.pack(fill=tk.BOTH, expand=True)

        # 模板信息显示
        info_frame = ttk.Frame(template_frame)
        info_frame.pack(fill=tk.X, padx=8, pady=(6, 4))

        ttk.Label(info_frame, text="当前模板:", font=('微软雅黑', 9)).pack(side=tk.LEFT)
        self.template_info_label = ttk.Label(
            info_frame,
            text="请选择单位和激光要求",
            foreground=self.colors['text_secondary'],
            font=('微软雅黑', 9)
        )
        self.template_info_label.pack(side=tk.LEFT, padx=(5, 0))

        # 模板代码显示区域（改为紧凑的文本框）
        text_frame = ttk.Frame(template_frame)
        text_frame.pack(fill=tk.X, padx=8, pady=(0, 6))

        # 使用Entry代替Text，更适合单行代码显示
        self.template_var = tk.StringVar()
        self.template_entry = tk.Entry(
            text_frame,
            textvariable=self.template_var,
            font=("Consolas", 10),
            bg=self.colors['surface'],
            fg=self.colors['text'],
            relief='flat',
            borderwidth=1,
            highlightthickness=1,
            highlightcolor=self.colors['primary'],
            state='readonly'
        )
        self.template_entry.pack(fill=tk.X, pady=2)

        # 如果需要多行显示，使用较小的Text组件
        self.template_text = tk.Text(
            text_frame,
            height=3,  # 只显示3行
            wrap=tk.WORD,
            font=("Consolas", 9),
            bg=self.colors['surface'],
            fg=self.colors['text'],
            selectbackground=self.colors['primary'],
            selectforeground="white",
            relief='flat',
            borderwidth=1,
            highlightthickness=1,
            highlightcolor=self.colors['primary'],
            state='disabled'
        )
        # 默认隐藏多行文本框
        # self.template_text.pack(fill=tk.X, pady=2)
        
        # 模板操作按钮
        template_btn_frame = ttk.Frame(template_frame)
        template_btn_frame.pack(fill=tk.X, padx=8, pady=(4, 6))

        # 左侧按钮组
        left_btn_group = ttk.Frame(template_btn_frame)
        left_btn_group.pack(side=tk.LEFT)

        copy_btn = ttk.Button(
            left_btn_group,
            text=f"{self.icons['copy']} 复制",
            command=self.copy_template,
            style='Modern.TButton'
        )
        copy_btn.pack(side=tk.LEFT, padx=(0, 4))

        clear_btn = ttk.Button(
            left_btn_group,
            text=f"{self.icons['clear']} 清空",
            command=self.clear_template_display,
            style='Modern.TButton'
        )
        clear_btn.pack(side=tk.LEFT, padx=(0, 4))

        # 右侧按钮组
        right_btn_group = ttk.Frame(template_btn_frame)
        right_btn_group.pack(side=tk.RIGHT)

        save_btn = ttk.Button(
            right_btn_group,
            text=f"{self.icons['save']} 保存到文件",
            command=self.save_template_to_file,
            style='Modern.TButton'
        )
        save_btn.pack(side=tk.RIGHT)

        # 现代化状态栏
        status_frame = ttk.Frame(self.root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=4, pady=2)

        self.status_bar = ttk.Label(
            status_frame,
            text="🟢 就绪",
            font=('微软雅黑', 8),
            foreground=self.colors['text_secondary'],
            background=self.colors['background']
        )
        self.status_bar.pack(side=tk.LEFT, padx=4)
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="刷新数据", command=self.refresh_data)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 设置菜单
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="设置", menu=settings_menu)
        settings_menu.add_command(label="数据管理", command=self.open_settings)
        settings_menu.add_checkbutton(label="窗口置顶", variable=self.always_on_top, command=self.toggle_topmost)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def load_units(self):
        """加载单位数据"""
        units = self.data_manager.get_units()

        # 更新下拉列表
        self.unit_combobox['values'] = units
        if units:
            self.unit_combobox.set('')

        # 更新快速选择按钮
        for widget in self.unit_buttons_frame.winfo_children():
            widget.destroy()

        for i, unit in enumerate(units):
            btn = ttk.Button(
                self.unit_buttons_frame,
                text=unit,
                command=lambda u=unit: self.select_unit(u),
                width=10,  # 减小按钮宽度
                style='Modern.TButton'
            )
            btn.pack(side=tk.LEFT, padx=2, pady=1)
            if i > 0 and (i + 1) % 5 == 0:  # 每5个按钮换行，更紧凑
                ttk.Frame(self.unit_buttons_frame).pack()

        # 清空其他显示
        self.req_listbox.delete(0, tk.END)
        self.template_var.set("")
        self.template_text.config(state='normal')
        self.template_text.delete(1.0, tk.END)
        self.template_text.config(state='disabled')
        self.update_status(f"📊 已加载 {len(units)} 个单位")
    
    def on_unit_selected(self, event):
        """下拉列表单位选择事件"""
        unit = self.unit_combobox.get()
        if unit:
            self.select_unit(unit)
    
    def select_unit(self, unit):
        """选择单位"""
        self.current_unit = unit
        self.current_requirement = None

        # 更新下拉列表显示
        self.unit_combobox.set(unit)

        # 加载激光要求
        self.load_requirements()

        # 清空模板显示
        self.template_var.set("")
        self.template_text.config(state='normal')
        self.template_text.delete(1.0, tk.END)
        self.template_text.config(state='disabled')
        self.update_template_info()

        self.update_status(f"🏢 已选择单位: {unit}")

    def load_requirements(self):
        """加载当前单位的激光要求"""
        self.req_listbox.delete(0, tk.END)

        if self.current_unit:
            requirements = self.data_manager.get_requirements(self.current_unit)
            for req in requirements:
                self.req_listbox.insert(tk.END, req)

            if requirements:
                self.update_status(f"📋 单位 '{self.current_unit}' 有 {len(requirements)} 个激光要求")
            else:
                self.update_status(f"⚠️ 单位 '{self.current_unit}' 暂无激光要求")
    
    def on_requirement_selected(self, event):
        """激光要求选择事件"""
        selection = self.req_listbox.curselection()
        if selection and self.current_unit:
            requirement = self.req_listbox.get(selection[0])
            self.select_requirement(requirement)
    
    def on_requirement_double_click(self, event):
        """激光要求双击事件"""
        self.on_requirement_selected(event)
        if self.current_requirement:
            self.copy_template()
    
    def select_requirement(self, requirement):
        """选择激光要求"""
        self.current_requirement = requirement

        # 加载模板代码
        template = self.data_manager.get_template(self.current_unit, requirement)

        # 判断是单行还是多行代码
        if '\n' in template and len(template.split('\n')) > 1:
            # 多行代码，显示Text组件
            self.template_entry.pack_forget()
            self.template_text.pack(fill=tk.X, pady=2)
            self.template_text.config(state='normal')
            self.template_text.delete(1.0, tk.END)
            self.template_text.insert(1.0, template)
            self.template_text.config(state='disabled')
        else:
            # 单行代码，显示Entry组件
            self.template_text.pack_forget()
            self.template_entry.pack(fill=tk.X, pady=2)
            self.template_var.set(template)

        self.update_template_info()
        self.update_status(f"✅ 已加载模板: {self.current_unit} - {requirement}")

    def update_template_info(self):
        """更新模板信息显示"""
        if self.current_unit and self.current_requirement:
            info = f"{self.current_unit} - {self.current_requirement}"
            self.template_info_label.config(text=info, foreground=self.colors['text'])
        else:
            self.template_info_label.config(text="请选择单位和激光要求", foreground=self.colors['text_secondary'])
    
    def copy_template(self):
        """复制模板代码到剪贴板"""
        # 获取当前显示的模板代码
        if self.template_entry.winfo_viewable():
            template = self.template_var.get().strip()
        else:
            template = self.template_text.get(1.0, tk.END).strip()

        if template:
            self.root.clipboard_clear()
            self.root.clipboard_append(template)
            self.update_status(f"{self.icons['check']} 模板代码已复制到剪贴板")
            messagebox.showinfo("复制成功", f"{self.icons['check']} 模板代码已复制到剪贴板！")
        else:
            messagebox.showwarning("警告", "没有可复制的模板代码！")

    def clear_template_display(self):
        """清空模板显示"""
        self.template_var.set("")
        self.template_text.config(state='normal')
        self.template_text.delete(1.0, tk.END)
        self.template_text.config(state='disabled')
        self.current_requirement = None
        self.update_template_info()
        self.update_status("🗑️ 已清空模板显示")
    
    def save_template_to_file(self):
        """保存模板代码到文件"""
        # 获取当前显示的模板代码
        if self.template_entry.winfo_viewable():
            template = self.template_var.get().strip()
        else:
            template = self.template_text.get(1.0, tk.END).strip()

        if not template:
            messagebox.showwarning("警告", "没有可保存的模板代码！")
            return

        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            title="保存模板代码",
            defaultextension=".gcode",
            filetypes=[("G代码文件", "*.gcode"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(template)
                messagebox.showinfo("保存成功", f"{self.icons['check']} 模板代码已保存到:\n{filename}")
                self.update_status(f"{self.icons['save']} 模板已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("保存失败", f"保存文件时出错: {e}")
    
    def toggle_topmost(self):
        """切换窗口置顶状态"""
        self.root.attributes('-topmost', self.always_on_top.get())
        status = "开启" if self.always_on_top.get() else "关闭"
        icon = self.icons['pin'] if self.always_on_top.get() else "📌"
        self.update_status(f"{icon} 窗口置顶已{status}")

    def refresh_data(self):
        """刷新数据"""
        self.data_manager.load_data()
        self.load_units()
        self.current_unit = None
        self.current_requirement = None
        self.update_template_info()
        messagebox.showinfo("刷新完成", f"{self.icons['check']} 数据已刷新！")
    
    def open_settings(self):
        """打开设置窗口"""
        SettingsWindow(self.root, self.data_manager, self.refresh_data)
    
    def show_help(self):
        """显示使用说明"""
        help_text = """激光模板查询器使用说明：

1. 选择单位：
   - 使用下拉列表选择单位
   - 或点击快速选择按钮

2. 选择激光要求：
   - 在激光要求列表中点击选择
   - 双击可直接复制模板代码

3. 查看模板代码：
   - 选择要求后自动显示对应的模板代码
   - 可复制代码到剪贴板
   - 可保存代码到文件

4. 数据管理：
   - 点击"数据设置"按钮进入管理界面
   - 可添加、编辑、删除单位和要求
   - 支持数据导入导出

5. 其他功能：
   - 窗口置顶：保持窗口在最前面
   - 刷新数据：重新加载数据文件"""
        
        messagebox.showinfo("使用说明", help_text)
    
    def show_about(self):
        """显示关于信息"""
        about_text = """激光模板查询器 v1.0

一个用于快速查询激光加工模板代码的桌面应用程序。

功能特点：
• 三级查询：单位 → 激光要求 → 模板代码
• 数据管理：支持添加、编辑、删除数据
• 数据持久化：JSON格式存储
• 导入导出：支持数据备份和恢复
• 窗口置顶：方便与其他软件配合使用

开发语言：Python + tkinter
版本：1.0
"""
        messagebox.showinfo("关于", about_text)
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_bar.config(text=message)
    
    def on_closing(self):
        """程序关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出激光模板查询器吗？"):
            self.root.destroy()
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = LaserTemplateQueryApp()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {e}")
        print(f"错误详情: {e}")


if __name__ == "__main__":
    main()
