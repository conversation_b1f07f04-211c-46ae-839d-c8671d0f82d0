# -*- coding: utf-8 -*-
"""
激光模板查询器主程序
提供三级查询功能：单位 -> 激光要求 -> 模板代码
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_manager import DataManager
from settings_window import SettingsWindow


class LaserTemplateQueryApp:
    def __init__(self):
        """初始化激光模板查询器应用"""
        self.root = tk.Tk()
        self.root.title("🔥 激光模板查询器 v1.0")
        self.root.geometry("750x480")
        self.root.minsize(650, 400)
        
        # 初始化数据管理器
        self.data_manager = DataManager()

        # 窗口置顶状态
        self.always_on_top = tk.BooleanVar(value=False)

        # 当前选中的数据
        self.current_unit = None
        self.current_requirement = None

        # 配置现代化样式
        self.setup_modern_style()

        # 创建界面
        self.create_widgets()
        self.load_units()

        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_modern_style(self):
        """配置现代化界面样式"""
        # 现代扁平化配色方案 - 更加鲜明的色彩
        self.colors = {
            # 主色调 - 更鲜明的蓝色系
            'primary': '#3b82f6',           # 明亮蓝色
            'primary_light': '#60a5fa',     # 浅蓝色
            'primary_dark': '#2563eb',      # 深蓝色
            'primary_hover': '#1d4ed8',     # 悬停蓝色

            # 辅助色调 - 更现代的灰色系
            'secondary': '#64748b',         # 中性灰
            'secondary_light': '#94a3b8',   # 浅灰色
            'accent': '#06b6d4',            # 青色强调色
            'accent_light': '#22d3ee',      # 浅青色

            # 状态色彩 - 更鲜明的状态色
            'success': '#10b981',           # 成功绿
            'success_light': '#34d399',     # 浅成功绿
            'warning': '#f59e0b',           # 警告橙
            'warning_light': '#fbbf24',     # 浅警告橙
            'danger': '#ef4444',            # 危险红
            'danger_light': '#f87171',      # 浅危险红

            # 背景色彩 - 更清爽的背景
            'background': '#f1f5f9',        # 浅灰背景
            'background_dark': '#e2e8f0',   # 稍深背景
            'surface': '#ffffff',           # 纯白表面
            'surface_hover': '#f8fafc',     # 悬停表面
            'card': '#ffffff',              # 卡片背景
            'card_hover': '#f9fafb',        # 卡片悬停

            # 文字色彩 - 更清晰的对比度
            'text': '#0f172a',              # 主文字 - 深灰黑
            'text_secondary': '#334155',    # 次要文字
            'text_muted': '#64748b',        # 静音文字
            'text_inverse': '#ffffff',      # 反色文字

            # 边框色彩 - 更明显的边框
            'border': '#cbd5e1',            # 默认边框
            'border_focus': '#3b82f6',      # 焦点边框
            'border_hover': '#94a3b8',      # 悬停边框

            # 阴影色彩 - 更明显的阴影效果
            'shadow_light': 'rgba(0, 0, 0, 0.05)',   # 极浅阴影
            'shadow_medium': 'rgba(0, 0, 0, 0.1)',   # 中等阴影
            'shadow_dark': 'rgba(0, 0, 0, 0.15)',    # 深阴影
        }

        # 现代扁平化图标系统 - 简洁统一
        self.icons = {
            # 主要操作图标
            'refresh': '↻',
            'settings': '⚙',
            'copy': '⧉',
            'save': '💾',
            'clear': '✕',
            'export': '↗',
            'import': '↙',

            # 编辑操作图标
            'add': '+',
            'delete': '✕',
            'edit': '✎',
            'rename': '✎',

            # 导航和状态图标
            'search': '⌕',
            'pin': '📌',
            'folder': '📁',
            'file': '📄',
            'check': '✓',
            'warning': '⚠',
            'error': '✕',
            'info': 'ⓘ',

            # 状态指示图标
            'ready': '●',
            'loading': '○',
            'success': '✓',
            'failed': '✕',

            # 功能图标
            'laser': '⚡',
            'template': '📋',
            'unit': '🏢',
            'requirement': '📝',
            'code': '⟨⟩',

            # 装饰图标
            'star': '★',
            'heart': '♥',
            'diamond': '◆',
            'sparkle': '✦'
        }

        # 配置高级ttk样式系统
        self.style = ttk.Style()

        # 现代扁平化按钮样式 - 更加圆润的按钮
        self.style.configure('Modern.TButton',
                           padding=(14, 10),
                           font=('微软雅黑', 9),
                           relief='flat',
                           borderwidth=0,
                           focuscolor='none',
                           background=self.colors['surface'],
                           foreground=self.colors['text'])

        # 主要按钮样式 - 更鲜明的蓝色主题
        self.style.configure('Primary.TButton',
                           padding=(16, 12),
                           font=('微软雅黑', 9, 'bold'),
                           relief='flat',
                           borderwidth=0,
                           focuscolor='none',
                           background=self.colors['primary'],
                           foreground=self.colors['text_inverse'])

        # 成功按钮样式 - 更鲜明的绿色主题
        self.style.configure('Success.TButton',
                           padding=(14, 10),
                           font=('微软雅黑', 9, 'bold'),
                           relief='flat',
                           borderwidth=0,
                           focuscolor='none',
                           background=self.colors['success'],
                           foreground=self.colors['text_inverse'])

        # 危险按钮样式 - 更鲜明的红色主题
        self.style.configure('Danger.TButton',
                           padding=(14, 10),
                           font=('微软雅黑', 9, 'bold'),
                           relief='flat',
                           borderwidth=0,
                           focuscolor='none',
                           background=self.colors['danger'],
                           foreground=self.colors['text_inverse'])

        # 现代扁平化标签框样式 - 更加圆润的边角
        self.style.configure('Modern.TLabelframe',
                           padding=18,
                           relief='flat',
                           borderwidth=0,
                           background=self.colors['surface'],
                           bordercolor=self.colors['border'])

        self.style.configure('Modern.TLabelframe.Label',
                           font=('微软雅黑', 12, 'bold'),
                           foreground=self.colors['primary'],
                           background=self.colors['surface'],
                           padding=(0, 2))

        # 现代组合框样式 - 更加扁平化
        self.style.configure('Modern.TCombobox',
                           padding=12,
                           font=('微软雅黑', 10),
                           relief='flat',
                           borderwidth=0,
                           focuscolor='none',
                           fieldbackground=self.colors['surface'],
                           bordercolor=self.colors['border'])

        # 现代复选框样式
        self.style.configure('Modern.TCheckbutton',
                           font=('微软雅黑', 9),
                           focuscolor='none',
                           background=self.colors['background'],
                           foreground=self.colors['text'])

        # 设置根窗口背景色 - 渐变效果
        self.root.configure(bg=self.colors['background'])

        # 创建自定义样式映射（悬停效果）
        self.setup_hover_effects()

    def setup_hover_effects(self):
        """配置悬停效果和状态映射"""
        # 现代扁平化悬停效果 - 更加明显的状态变化
        self.style.map('Modern.TButton',
                      background=[('active', self.colors['background_dark']),
                                ('pressed', self.colors['primary_light'])],
                      foreground=[('active', self.colors['primary']),
                                ('pressed', self.colors['text_inverse'])],
                      bordercolor=[('active', self.colors['primary_light']),
                                 ('pressed', self.colors['primary'])],
                      relief=[('pressed', 'flat'),
                            ('!pressed', 'flat')])

        self.style.map('Primary.TButton',
                      background=[('active', self.colors['primary_light']),
                                ('pressed', self.colors['primary_dark'])],
                      foreground=[('active', self.colors['text_inverse']),
                                ('pressed', self.colors['text_inverse'])],
                      relief=[('pressed', 'flat'),
                            ('!pressed', 'flat')])

        self.style.map('Success.TButton',
                      background=[('active', self.colors['success_light']),
                                ('pressed', self.colors['success'])],
                      foreground=[('active', self.colors['text_inverse']),
                                ('pressed', self.colors['text_inverse'])],
                      relief=[('pressed', 'flat'),
                            ('!pressed', 'flat')])

        self.style.map('Danger.TButton',
                      background=[('active', self.colors['danger_light']),
                                ('pressed', self.colors['danger'])],
                      foreground=[('active', self.colors['text_inverse']),
                                ('pressed', self.colors['text_inverse'])],
                      relief=[('pressed', 'flat'),
                            ('!pressed', 'flat')])

        # 组合框悬停效果 - 更加明显的焦点状态
        self.style.map('Modern.TCombobox',
                      fieldbackground=[('readonly', self.colors['surface']),
                                     ('focus', self.colors['surface_hover'])],
                      bordercolor=[('focus', self.colors['primary']),
                                 ('!focus', self.colors['border'])],
                      arrowcolor=[('active', self.colors['primary']),
                                ('!active', self.colors['secondary'])])

        # 复选框悬停效果 - 更加明显的状态变化
        self.style.configure('Modern.TCheckbutton',
                           font=('微软雅黑', 10),
                           background=self.colors['background'],
                           foreground=self.colors['text'])
                           
        self.style.map('Modern.TCheckbutton',
                      background=[('active', self.colors['background']),
                                ('!active', self.colors['background'])],
                      foreground=[('active', self.colors['primary']),
                                ('!active', self.colors['text'])],
                      indicatorcolor=[('selected', self.colors['primary']),
                                    ('!selected', self.colors['surface'])])

    def create_gradient_frame(self, parent, color1, color2):
        """创建渐变背景框架（模拟效果）"""
        frame = tk.Frame(parent, bg=color1, relief='flat', bd=0)
        return frame

    def add_button_hover_effect(self, button):
        """为按钮添加悬停效果"""
        def on_enter(event):
            button.configure(cursor='hand2')

        def on_leave(event):
            button.configure(cursor='')

        button.bind('<Enter>', on_enter)
        button.bind('<Leave>', on_leave)

    def create_modern_listbox(self, parent, **kwargs):
        """创建现代扁平化列表框"""
        # 创建容器框架 - 扁平化设计
        container = tk.Frame(
            parent, 
            bg=self.colors['surface'], 
            relief='flat', 
            bd=1,
            highlightbackground=self.colors['border'],
            highlightthickness=1
        )

        # 创建列表框 - 现代扁平样式
        listbox = tk.Listbox(
            container,
            font=("微软雅黑", 9),
            selectbackground=self.colors['primary'],
            selectforeground=self.colors['text_inverse'],
            bg=self.colors['surface'],
            fg=self.colors['text'],
            relief='flat',
            borderwidth=0,
            highlightthickness=0,
            activestyle='none',
            selectmode=tk.SINGLE,
            **kwargs
        )

        # 创建现代滚动条
        scrollbar = ttk.Scrollbar(container, orient=tk.VERTICAL, command=listbox.yview)
        listbox.configure(yscrollcommand=scrollbar.set)

        # 布局 - 更紧凑
        listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=1, pady=1)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 1), pady=1)

        return container, listbox
    
    def create_widgets(self):
        """创建主界面组件"""
        # 创建菜单栏
        self.create_menu()

        # 主框架 - 更加现代的扁平化设计
        main_frame = tk.Frame(
            self.root, 
            bg=self.colors['background'], 
            relief='flat', 
            bd=0
        )
        main_frame.pack(fill=tk.BOTH, expand=True, padx=12, pady=12)

        # 顶部控制面板 - 更加现代的卡片设计
        control_frame = tk.Frame(
            main_frame, 
            bg=self.colors['surface'], 
            relief='flat', 
            bd=0,
            highlightbackground=self.colors['border'],
            highlightthickness=1
        )
        control_frame.pack(fill=tk.X, pady=(0, 12), padx=0)

        # 控制面板内容 - 更加宽敞的内边距
        control_inner = tk.Frame(control_frame, bg=self.colors['surface'])
        control_inner.pack(fill=tk.X, padx=20, pady=16)

        # 左侧控制组
        left_controls = tk.Frame(control_inner, bg=self.colors['card'])
        left_controls.pack(side=tk.LEFT)

        # 窗口置顶复选框（美化版）
        self.topmost_check = ttk.Checkbutton(
            left_controls,
            text=f"{self.icons['pin']} 窗口置顶",
            variable=self.always_on_top,
            command=self.toggle_topmost,
            style='Modern.TCheckbutton'
        )
        self.topmost_check.pack(side=tk.LEFT)

        # 刷新按钮（紧凑版）
        refresh_btn = ttk.Button(
            left_controls,
            text=f"{self.icons['refresh']} 刷新",
            command=self.refresh_data,
            style='Modern.TButton'
        )
        refresh_btn.pack(side=tk.LEFT, padx=(6, 0))

        # 右侧控制组
        right_controls = tk.Frame(control_inner, bg=self.colors['card'])
        right_controls.pack(side=tk.RIGHT)

        # 设置按钮（美化版主要按钮）
        settings_btn = ttk.Button(
            right_controls,
            text=f"{self.icons['settings']} 数据设置",
            command=self.open_settings,
            style='Primary.TButton'
        )
        settings_btn.pack(side=tk.RIGHT)

        # 为按钮添加悬停效果
        self.add_button_hover_effect(refresh_btn)
        self.add_button_hover_effect(settings_btn)
        
        # 创建三级查询面板
        query_frame = tk.Frame(main_frame, bg=self.colors['background'])
        query_frame.pack(fill=tk.BOTH, expand=True)

        # 第一级：单位选择 - 更加现代的扁平化设计
        unit_frame = ttk.LabelFrame(
            query_frame,
            text=f"{self.icons['unit']} 选择单位",
            style='Modern.TLabelframe'
        )
        unit_frame.pack(fill=tk.X, pady=(0, 12), padx=0)

        # 单位选择方式框架 - 更加宽敞的布局
        unit_select_frame = tk.Frame(unit_frame, bg=self.colors['surface'])
        unit_select_frame.pack(fill=tk.X, padx=16, pady=12)

        # 下拉列表方式 - 更加现代的样式
        dropdown_frame = tk.Frame(unit_select_frame, bg=self.colors['surface'])
        dropdown_frame.pack(side=tk.LEFT)

        ttk.Label(
            dropdown_frame,
            text="下拉选择:",
            font=('微软雅黑', 10, 'bold'),
            background=self.colors['surface'],
            foreground=self.colors['primary']
        ).pack(side=tk.LEFT)

        self.unit_combobox = ttk.Combobox(
            dropdown_frame,
            state="readonly",
            width=20,
            style='Modern.TCombobox',
            font=('微软雅黑', 10)
        )
        self.unit_combobox.pack(side=tk.LEFT, padx=(10, 0))
        self.unit_combobox.bind('<<ComboboxSelected>>', self.on_unit_selected)

        # 分隔线 - 更加明显的分隔效果
        separator = tk.Frame(unit_select_frame, width=2, bg=self.colors['border'])
        separator.pack(side=tk.LEFT, fill=tk.Y, padx=16)

        # 按钮选择方式 - 更加现代的样式
        buttons_section = tk.Frame(unit_select_frame, bg=self.colors['surface'])
        buttons_section.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Label(
            buttons_section,
            text="快速选择:",
            font=('微软雅黑', 10, 'bold'),
            background=self.colors['surface'],
            foreground=self.colors['primary']
        ).pack(side=tk.LEFT)

        self.unit_buttons_frame = tk.Frame(buttons_section, bg=self.colors['surface'])
        self.unit_buttons_frame.pack(side=tk.LEFT, padx=(12, 0), fill=tk.X, expand=True)
        
        # 第二级：激光要求选择 - 现代扁平化
        req_frame = ttk.LabelFrame(
            query_frame,
            text=f"{self.icons['requirement']} 选择激光要求",
            style='Modern.TLabelframe'
        )
        req_frame.pack(fill=tk.X, pady=(0, 8), padx=0)

        # 要求列表框架 - 扁平化布局
        req_list_container = tk.Frame(req_frame, bg=self.colors['surface'])
        req_list_container.pack(fill=tk.X, padx=12, pady=8)

        # 使用现代化列表框
        self.req_listbox_container, self.req_listbox = self.create_modern_listbox(
            req_list_container,
            height=4
        )
        self.req_listbox_container.pack(fill=tk.BOTH, expand=True)

        # 绑定事件
        self.req_listbox.bind('<<ListboxSelect>>', self.on_requirement_selected)
        self.req_listbox.bind('<Double-Button-1>', self.on_requirement_double_click)

        # 添加提示文本
        hint_label = ttk.Label(
            req_list_container,
            text="💡 提示：双击激光要求可直接复制模板代码",
            font=('微软雅黑', 8),
            foreground=self.colors['text_muted'],
            background=self.colors['surface']
        )
        hint_label.pack(pady=(4, 0))
        
        # 第三级：模板代码显示 - 现代扁平化
        template_frame = ttk.LabelFrame(
            query_frame,
            text=f"{self.icons['code']} 激光模板代码",
            style='Modern.TLabelframe'
        )
        template_frame.pack(fill=tk.BOTH, expand=True, padx=0)

        # 模板信息显示 - 扁平化布局
        info_frame = tk.Frame(template_frame, bg=self.colors['surface'])
        info_frame.pack(fill=tk.X, padx=12, pady=(8, 6))

        # 状态指示器
        status_frame = tk.Frame(info_frame, bg=self.colors['surface'])
        status_frame.pack(side=tk.LEFT)

        self.status_indicator = ttk.Label(
            status_frame,
            text=self.icons['ready'],
            font=('微软雅黑', 10),
            background=self.colors['surface']
        )
        self.status_indicator.pack(side=tk.LEFT)

        ttk.Label(
            status_frame,
            text="当前模板:",
            font=('微软雅黑', 9, 'bold'),
            background=self.colors['surface'],
            foreground=self.colors['text']
        ).pack(side=tk.LEFT, padx=(4, 0))

        self.template_info_label = ttk.Label(
            info_frame,
            text="请选择单位和激光要求",
            foreground=self.colors['text_secondary'],
            font=('微软雅黑', 9),
            background=self.colors['surface']
        )
        self.template_info_label.pack(side=tk.LEFT, padx=(8, 0))

        # 模板代码显示区域 - 现代扁平化
        text_container = tk.Frame(template_frame, bg=self.colors['surface'])
        text_container.pack(fill=tk.X, padx=12, pady=(0, 8))

        # 代码显示框架 - 扁平化卡片设计
        code_frame = tk.Frame(
            text_container,
            bg=self.colors['card'],
            relief='flat',
            bd=0,
            highlightbackground=self.colors['border'],
            highlightthickness=1
        )
        code_frame.pack(fill=tk.X, pady=4)

        # 代码内容框架 - 更大的内边距
        code_inner = tk.Frame(code_frame, bg=self.colors['card'])
        code_inner.pack(fill=tk.X, padx=12, pady=8)

        # 单行代码显示（Entry）
        self.template_var = tk.StringVar()
        self.template_entry = tk.Entry(
            code_inner,
            textvariable=self.template_var,
            font=("Consolas", 10),
            bg=self.colors['card'],
            fg=self.colors['text'],
            relief='flat',
            borderwidth=0,
            highlightthickness=0,
            state='readonly',
            readonlybackground=self.colors['card']
        )
        self.template_entry.pack(fill=tk.X, pady=2)

        # 多行代码显示（Text）
        self.template_text = tk.Text(
            code_inner,
            height=3,
            wrap=tk.WORD,
            font=("Consolas", 9),
            bg=self.colors['card'],
            fg=self.colors['text'],
            selectbackground=self.colors['primary'],
            selectforeground=self.colors['text_inverse'],
            relief='flat',
            borderwidth=0,
            highlightthickness=0,
            state='disabled'
        )
        # 默认隐藏多行文本框
        
        # 模板操作按钮 - 现代扁平化
        btn_container = tk.Frame(template_frame, bg=self.colors['surface'])
        btn_container.pack(fill=tk.X, padx=12, pady=(4, 8))

        # 按钮框架 - 扁平化设计
        template_btn_frame = tk.Frame(
            btn_container, 
            bg=self.colors['background'], 
            relief='flat', 
            bd=0,
            highlightbackground=self.colors['border'],
            highlightthickness=1
        )
        template_btn_frame.pack(fill=tk.X, pady=2)

        # 内部按钮容器 - 更大的内边距
        btn_inner = tk.Frame(template_btn_frame, bg=self.colors['background'])
        btn_inner.pack(fill=tk.X, padx=12, pady=8)

        # 左侧按钮组
        left_btn_group = tk.Frame(btn_inner, bg=self.colors['background_dark'])
        left_btn_group.pack(side=tk.LEFT)

        # 复制按钮 - 成功样式，紧凑版
        copy_btn = ttk.Button(
            left_btn_group,
            text=f"{self.icons['copy']} 复制",
            command=self.copy_template,
            style='Success.TButton'
        )
        copy_btn.pack(side=tk.LEFT, padx=(0, 3))

        # 清空按钮 - 普通样式，紧凑版
        clear_btn = ttk.Button(
            left_btn_group,
            text=f"{self.icons['clear']} 清空",
            command=self.clear_template_display,
            style='Modern.TButton'
        )
        clear_btn.pack(side=tk.LEFT, padx=(0, 3))

        # 右侧按钮组
        right_btn_group = tk.Frame(btn_inner, bg=self.colors['background_dark'])
        right_btn_group.pack(side=tk.RIGHT)

        # 保存按钮 - 主要样式
        save_btn = ttk.Button(
            right_btn_group,
            text=f"{self.icons['save']} 保存到文件",
            command=self.save_template_to_file,
            style='Primary.TButton'
        )
        save_btn.pack(side=tk.RIGHT)

        # 为所有按钮添加悬停效果
        for btn in [copy_btn, clear_btn, save_btn]:
            self.add_button_hover_effect(btn)

        # 现代扁平化状态栏
        status_container = tk.Frame(
            self.root, 
            bg=self.colors['surface'], 
            height=32,
            highlightbackground=self.colors['border'],
            highlightthickness=1
        )
        status_container.pack(side=tk.BOTTOM, fill=tk.X)
        status_container.pack_propagate(False)

        # 状态栏内容 - 扁平化布局
        status_inner = tk.Frame(status_container, bg=self.colors['surface'])
        status_inner.pack(fill=tk.BOTH, expand=True, padx=12, pady=6)

        # 左侧状态信息
        status_left = tk.Frame(status_inner, bg=self.colors['surface'])
        status_left.pack(side=tk.LEFT)

        self.status_bar = tk.Label(
            status_left,
            text=f"{self.icons['ready']} 就绪",
            font=('微软雅黑', 9),
            fg=self.colors['text_secondary'],
            bg=self.colors['surface']
        )
        self.status_bar.pack(side=tk.LEFT)

        # 右侧信息显示
        status_right = tk.Frame(status_inner, bg=self.colors['surface'])
        status_right.pack(side=tk.RIGHT)

        # 版本信息
        version_label = tk.Label(
            status_right,
            text=f"{self.icons['sparkle']} 激光模板查询器 v2.0",
            font=('微软雅黑', 9),
            fg=self.colors['text_muted'],
            bg=self.colors['surface']
        )
        version_label.pack(side=tk.RIGHT)
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="刷新数据", command=self.refresh_data)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 设置菜单
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="设置", menu=settings_menu)
        settings_menu.add_command(label="数据管理", command=self.open_settings)
        settings_menu.add_checkbutton(label="窗口置顶", variable=self.always_on_top, command=self.toggle_topmost)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def load_units(self):
        """加载单位数据"""
        units = self.data_manager.get_units()

        # 更新下拉列表
        self.unit_combobox['values'] = units
        if units:
            self.unit_combobox.set('')

        # 更新快速选择按钮
        for widget in self.unit_buttons_frame.winfo_children():
            widget.destroy()

        for i, unit in enumerate(units):
            btn = ttk.Button(
                self.unit_buttons_frame,
                text=unit,
                command=lambda u=unit: self.select_unit(u),
                width=8,  # 减小按钮宽度
                style='Modern.TButton'
            )
            btn.pack(side=tk.LEFT, padx=2, pady=1)  # 减小间距

            # 为每个按钮添加悬停效果
            self.add_button_hover_effect(btn)

            # 每5个按钮换行，更紧凑
            if i > 0 and (i + 1) % 5 == 0:
                tk.Frame(self.unit_buttons_frame, height=2, bg=self.colors['surface']).pack()

        # 清空其他显示
        self.req_listbox.delete(0, tk.END)
        self.template_var.set("")
        self.template_text.config(state='normal')
        self.template_text.delete(1.0, tk.END)
        self.template_text.config(state='disabled')
        self.update_status(f"📊 已加载 {len(units)} 个单位")
    
    def on_unit_selected(self, event):
        """下拉列表单位选择事件"""
        unit = self.unit_combobox.get()
        if unit:
            self.select_unit(unit)
    
    def select_unit(self, unit):
        """选择单位"""
        self.current_unit = unit
        self.current_requirement = None

        # 更新下拉列表显示
        self.unit_combobox.set(unit)

        # 加载激光要求
        self.load_requirements()

        # 清空模板显示
        self.template_var.set("")
        self.template_text.config(state='normal')
        self.template_text.delete(1.0, tk.END)
        self.template_text.config(state='disabled')
        self.update_template_info()

        self.update_status(f"🏢 已选择单位: {unit}")

    def load_requirements(self):
        """加载当前单位的激光要求"""
        self.req_listbox.delete(0, tk.END)

        if self.current_unit:
            requirements = self.data_manager.get_requirements(self.current_unit)
            for req in requirements:
                self.req_listbox.insert(tk.END, req)

            if requirements:
                self.update_status(f"📋 单位 '{self.current_unit}' 有 {len(requirements)} 个激光要求")
            else:
                self.update_status(f"⚠️ 单位 '{self.current_unit}' 暂无激光要求")
    
    def on_requirement_selected(self, event):
        """激光要求选择事件"""
        selection = self.req_listbox.curselection()
        if selection and self.current_unit:
            requirement = self.req_listbox.get(selection[0])
            self.select_requirement(requirement)
    
    def on_requirement_double_click(self, event):
        """激光要求双击事件"""
        self.on_requirement_selected(event)
        if self.current_requirement:
            self.copy_template()
    
    def select_requirement(self, requirement):
        """选择激光要求"""
        self.current_requirement = requirement

        # 加载模板代码
        template = self.data_manager.get_template(self.current_unit, requirement)

        # 判断是单行还是多行代码
        if '\n' in template and len(template.split('\n')) > 1:
            # 多行代码，显示Text组件
            self.template_entry.pack_forget()
            self.template_text.pack(fill=tk.X, pady=2)
            self.template_text.config(state='normal')
            self.template_text.delete(1.0, tk.END)
            self.template_text.insert(1.0, template)
            self.template_text.config(state='disabled')
        else:
            # 单行代码，显示Entry组件
            self.template_text.pack_forget()
            self.template_entry.pack(fill=tk.X, pady=2)
            self.template_var.set(template)

        self.update_template_info()
        self.update_status(f"✅ 已加载模板: {self.current_unit} - {requirement}")

    def update_template_info(self):
        """更新模板信息显示 - 美化版"""
        if self.current_unit and self.current_requirement:
            info = f"{self.current_unit} - {self.current_requirement}"
            self.template_info_label.config(text=info, foreground=self.colors['text'])
            self.status_indicator.config(text=self.icons['success'])
        elif self.current_unit:
            info = f"{self.current_unit} - 请选择激光要求"
            self.template_info_label.config(text=info, foreground=self.colors['text_secondary'])
            self.status_indicator.config(text=self.icons['warning'])
        else:
            self.template_info_label.config(text="请选择单位和激光要求", foreground=self.colors['text_secondary'])
            self.status_indicator.config(text=self.icons['ready'])
    
    def copy_template(self):
        """复制模板代码到剪贴板"""
        # 获取当前显示的模板代码
        if self.template_entry.winfo_viewable():
            template = self.template_var.get().strip()
        else:
            template = self.template_text.get(1.0, tk.END).strip()

        if template:
            self.root.clipboard_clear()
            self.root.clipboard_append(template)
            self.update_status(f"{self.icons['check']} 模板代码已复制到剪贴板")
            messagebox.showinfo("复制成功", f"{self.icons['check']} 模板代码已复制到剪贴板！")
        else:
            messagebox.showwarning("警告", "没有可复制的模板代码！")

    def clear_template_display(self):
        """清空模板显示"""
        self.template_var.set("")
        self.template_text.config(state='normal')
        self.template_text.delete(1.0, tk.END)
        self.template_text.config(state='disabled')
        self.current_requirement = None
        self.update_template_info()
        self.update_status("🗑️ 已清空模板显示")
    
    def save_template_to_file(self):
        """保存模板代码到文件"""
        # 获取当前显示的模板代码
        if self.template_entry.winfo_viewable():
            template = self.template_var.get().strip()
        else:
            template = self.template_text.get(1.0, tk.END).strip()

        if not template:
            messagebox.showwarning("警告", "没有可保存的模板代码！")
            return

        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            title="保存模板代码",
            defaultextension=".gcode",
            filetypes=[("G代码文件", "*.gcode"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(template)
                messagebox.showinfo("保存成功", f"{self.icons['check']} 模板代码已保存到:\n{filename}")
                self.update_status(f"{self.icons['save']} 模板已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("保存失败", f"保存文件时出错: {e}")
    
    def toggle_topmost(self):
        """切换窗口置顶状态"""
        self.root.attributes('-topmost', self.always_on_top.get())
        status = "开启" if self.always_on_top.get() else "关闭"
        icon = self.icons['pin'] if self.always_on_top.get() else "📌"
        self.update_status(f"{icon} 窗口置顶已{status}")

    def refresh_data(self):
        """刷新数据"""
        self.data_manager.load_data()
        self.load_units()
        self.current_unit = None
        self.current_requirement = None
        self.update_template_info()
        messagebox.showinfo("刷新完成", f"{self.icons['check']} 数据已刷新！")
    
    def open_settings(self):
        """打开设置窗口"""
        SettingsWindow(self.root, self.data_manager, self.refresh_data)
    
    def show_help(self):
        """显示使用说明"""
        help_text = """激光模板查询器使用说明：

1. 选择单位：
   - 使用下拉列表选择单位
   - 或点击快速选择按钮

2. 选择激光要求：
   - 在激光要求列表中点击选择
   - 双击可直接复制模板代码

3. 查看模板代码：
   - 选择要求后自动显示对应的模板代码
   - 可复制代码到剪贴板
   - 可保存代码到文件

4. 数据管理：
   - 点击"数据设置"按钮进入管理界面
   - 可添加、编辑、删除单位和要求
   - 支持数据导入导出

5. 其他功能：
   - 窗口置顶：保持窗口在最前面
   - 刷新数据：重新加载数据文件"""
        
        messagebox.showinfo("使用说明", help_text)
    
    def show_about(self):
        """显示关于信息"""
        about_text = """激光模板查询器 v1.0

一个用于快速查询激光加工模板代码的桌面应用程序。

功能特点：
• 三级查询：单位 → 激光要求 → 模板代码
• 数据管理：支持添加、编辑、删除数据
• 数据持久化：JSON格式存储
• 导入导出：支持数据备份和恢复
• 窗口置顶：方便与其他软件配合使用

开发语言：Python + tkinter
版本：1.0
"""
        messagebox.showinfo("关于", about_text)
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_bar.config(text=message)
    
    def on_closing(self):
        """程序关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出激光模板查询器吗？"):
            self.root.destroy()
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = LaserTemplateQueryApp()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {e}")
        print(f"错误详情: {e}")


if __name__ == "__main__":
    main()
