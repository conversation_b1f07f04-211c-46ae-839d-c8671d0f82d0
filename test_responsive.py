#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
响应式布局测试脚本
用于验证窗口大小变化时的响应式效果
"""

import tkinter as tk
from main import LaserTemplateQueryApp
import time

def test_responsive():
    """测试响应式布局"""
    print("启动响应式布局测试...")
    
    # 创建应用实例
    app = LaserTemplateQueryApp()
    
    def auto_resize():
        """自动调整窗口大小进行测试"""
        sizes = [
            (750, 480),  # 默认大小
            (600, 400),  # 中等大小
            (500, 350),  # 最小大小
            (900, 600),  # 较大大小
            (750, 480),  # 回到默认
        ]
        
        for i, (width, height) in enumerate(sizes):
            app.root.after(i * 2000, lambda w=width, h=height: app.root.geometry(f"{w}x{h}"))
            print(f"计划在 {i*2} 秒后调整到 {width}x{height}")
    
    # 启动自动调整大小测试
    app.root.after(1000, auto_resize)
    
    # 运行应用
    app.run()

if __name__ == "__main__":
    test_responsive()
